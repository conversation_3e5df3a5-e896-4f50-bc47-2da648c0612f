# AnimatedGIF for ESP-IDF

这是将Arduino平台的AnimatedGIF库改造为适配ESP-IDF的版本。

## 主要修改

1. **头文件适配** (`AnimatedGIF.h`)
   - 添加了 `ESP_PLATFORM` 宏支持
   - 更新了条件编译指令以支持ESP-IDF环境

2. **源文件转换**
   - 将 `gif.inl` 转换为 `esp_gif.c`，因为ESP-IDF不支持.inl文件
   - 更新了ESP32相关的宏定义

3. **Arduino函数替换** (`AnimatedGIF.cpp`)
   - 将 `millis()` 替换为 `xTaskGetTickCount() * portTICK_PERIOD_MS`
   - 将 `delay()` 替换为 `vTaskDelay()`
   - 添加了必要的FreeRTOS头文件包含

4. **ESP-IDF兼容的GIFPlayer** (`GIFPlayer_ESP32.h`)
   - 移除了Arduino特定的依赖（SD库、bb_spi_lcd等）
   - 使用ESP-IDF的内存分配函数（heap_caps_malloc）
   - 提供了通用的显示接口

## 文件结构

```
src/
├── AnimatedGIF.h          # 主头文件（已适配ESP-IDF）
├── AnimatedGIF.cpp        # 主实现文件（已适配ESP-IDF）
├── esp_gif.c              # GIF解码核心代码（从gif.inl转换）
├── GIFPlayer_ESP32.h      # ESP-IDF兼容的GIF播放器
└── esp_gif_example.c      # 使用示例
```

## 使用方法

### 1. 基本使用

```cpp
#include "AnimatedGIF.h"

// 创建GIF解码器实例
AnimatedGIF gif;

// 绘制回调函数
void GIFDraw(GIFDRAW *pDraw) {
    // 将解码后的像素发送到显示屏
    // pDraw->pPixels 包含当前行的像素数据
    // pDraw->iWidth, pDraw->iHeight 是尺寸信息
}

// 初始化并播放GIF
void play_gif() {
    gif.begin(GIF_PALETTE_RGB565_LE);
    
    if (gif.open(gif_data, gif_size, GIFDraw)) {
        while (1) {
            int result = gif.playFrame(true, NULL);
            if (result <= 0) {
                gif.reset(); // 重新开始动画
            }
        }
        gif.close();
    }
}
```

### 2. 使用ESP-IDF兼容的GIFPlayer

```cpp
#include "GIFPlayer_ESP32.h"

// 定义显示接口
esp_display_t display = {
    .width = 240,
    .height = 320,
    .setAddrWindow = my_set_addr_window,
    .pushPixels = my_push_pixels
};

// 播放GIF
GIFPlayer_ESP32 player;
if (player.openData(&display, gif_data, gif_size) == GIF_SUCCESS) {
    while (1) {
        int result = player.play(GIF_CENTER, GIF_CENTER, true);
        if (result <= 0) break;
    }
    player.close();
}
```

### 3. 内存管理

在ESP-IDF中，建议使用PSRAM来存储帧缓冲区：

```cpp
// 库会自动尝试使用PSRAM
// 如果PSRAM不可用，会回退到内部RAM
```

## 编译配置

在你的ESP-IDF项目中：

1. 将 `src/` 目录复制到你的组件目录
2. 在 `CMakeLists.txt` 中添加：

```cmake
idf_component_register(
    SRCS "AnimatedGIF.cpp" "esp_gif.c" "esp_gif_example.c"
    INCLUDE_DIRS "."
    REQUIRES freertos esp_heap_caps
)
```

## 注意事项

1. **内存需求**: GIF解码需要较多内存，建议在有PSRAM的ESP32上使用
2. **性能**: 大尺寸GIF可能需要较长的解码时间
3. **显示接口**: 需要根据你的显示屏实现相应的接口函数
4. **文件系统**: 如果从文件播放GIF，需要先初始化相应的文件系统（SPIFFS、FAT等）

## 兼容性

- ESP32系列芯片
- ESP-IDF v4.0+
- 支持C++编译

## 示例

详细的使用示例请参考 `esp_gif_example.c` 文件。
