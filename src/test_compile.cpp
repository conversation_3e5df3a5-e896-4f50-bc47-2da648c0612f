//
// 编译测试文件 - 验证ESP-IDF适配是否成功
//
#include "AnimatedGIF.h"
#include "GIFPlayer_ESP32.h"

// 测试基本功能
void test_basic_functionality()
{
    // 测试AnimatedGIF类
    AnimatedGIF gif;
    gif.begin(GIF_PALETTE_RGB565_LE);
    
    // 测试获取错误信息
    int error = gif.getLastError();
    
    // 测试GIFPlayer_ESP32类
    GIFPlayer_ESP32 player;
    
    // 测试显示接口结构
    esp_display_t display = {
        .width = 240,
        .height = 320,
        .setAddrWindow = nullptr,
        .pushPixels = nullptr
    };
}

// 测试回调函数
void testGIFDraw(GIFDRAW *pDraw)
{
    // 简单的回调函数测试
    if (pDraw) {
        // 访问绘制参数
        int x = pDraw->iX;
        int y = pDraw->iY;
        int width = pDraw->iWidth;
        int height = pDraw->iHeight;
    }
}

// 测试C接口
extern "C" void test_c_interface()
{
    GIFIMAGE gif;
    GIF_begin(&gif, GIF_PALETTE_RGB565_LE);
    
    int width = GIF_getCanvasWidth(&gif);
    int height = GIF_getCanvasHeight(&gif);
    int error = GIF_getLastError(&gif);
}

int main()
{
    test_basic_functionality();
    test_c_interface();
    return 0;
}
