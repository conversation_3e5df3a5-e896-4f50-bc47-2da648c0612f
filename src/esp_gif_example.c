//
// ESP-IDF GIF Player Example
// 演示如何在ESP-IDF中使用AnimatedGIF库
//
#include "AnimatedGIF.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

static const char *TAG = "GIF_EXAMPLE";

// 示例：从内存播放GIF的回调函数
void GIFDraw(GIFDRAW *pDraw)
{
    // 这里可以将解码后的像素数据发送到显示屏
    // pDraw->pPixels 包含当前行的像素数据
    // pDraw->iWidth, pDraw->iHeight 是当前帧的尺寸
    // pDraw->x, pDraw->y 是当前行在画布中的位置
    
    ESP_LOGI(TAG, "Drawing line %d, width=%d, x=%d, y=%d", 
             pDraw->y, pDraw->iWidth, pDraw->iX, pDraw->iY);
    
    // 示例：将RGB565像素数据发送到SPI显示屏
    // spi_lcd_write_pixels(pDraw->iX, pDraw->iY + pDraw->y, 
    //                      pDraw->iWidth, 1, pDraw->pPixels);
}

// 示例：播放存储在内存中的GIF
void play_gif_from_memory(const uint8_t *gif_data, size_t gif_size)
{
    AnimatedGIF gif;
    int frameDelay;
    
    // 初始化GIF解码器
    gif.begin(GIF_PALETTE_RGB565_LE);
    
    // 打开GIF数据
    if (gif.open((uint8_t*)gif_data, gif_size, GIFDraw)) {
        ESP_LOGI(TAG, "GIF opened successfully");
        ESP_LOGI(TAG, "Canvas size: %dx%d", gif.getCanvasWidth(), gif.getCanvasHeight());
        
        // 播放GIF动画
        while (1) {
            int result = gif.playFrame(true, &frameDelay);
            if (result == 0) {
                // 动画结束，重新开始
                gif.reset();
                ESP_LOGI(TAG, "Animation loop completed, restarting...");
            } else if (result < 0) {
                // 发生错误
                ESP_LOGE(TAG, "Error playing frame: %d", gif.getLastError());
                break;
            }
            
            // 等待帧延迟时间
            if (frameDelay > 0) {
                vTaskDelay(pdMS_TO_TICKS(frameDelay));
            }
        }
        
        gif.close();
    } else {
        ESP_LOGE(TAG, "Failed to open GIF: %d", gif.getLastError());
    }
}

// 文件读取回调函数示例（需要根据实际文件系统实现）
int32_t fileRead(GIFFILE *pFile, uint8_t *pBuf, int32_t iLen)
{
    FILE *f = (FILE*)pFile->fHandle;
    return fread(pBuf, 1, iLen, f);
}

int32_t fileSeek(GIFFILE *pFile, int32_t iPosition)
{
    FILE *f = (FILE*)pFile->fHandle;
    fseek(f, iPosition, SEEK_SET);
    pFile->iPos = ftell(f);
    return pFile->iPos;
}

void* fileOpen(const char *filename, int32_t *pFileSize)
{
    FILE *f = fopen(filename, "rb");
    if (f) {
        fseek(f, 0, SEEK_END);
        *pFileSize = ftell(f);
        fseek(f, 0, SEEK_SET);
    }
    return f;
}

void fileClose(void *handle)
{
    if (handle) {
        fclose((FILE*)handle);
    }
}

// 示例：从文件播放GIF
void play_gif_from_file(const char *filename)
{
    AnimatedGIF gif;
    int frameDelay;
    
    // 初始化GIF解码器
    gif.begin(GIF_PALETTE_RGB565_LE);
    
    // 打开GIF文件
    if (gif.open(filename, fileOpen, fileClose, fileRead, fileSeek, GIFDraw)) {
        ESP_LOGI(TAG, "GIF file opened successfully: %s", filename);
        ESP_LOGI(TAG, "Canvas size: %dx%d", gif.getCanvasWidth(), gif.getCanvasHeight());
        
        // 播放GIF动画
        while (1) {
            int result = gif.playFrame(true, &frameDelay);
            if (result == 0) {
                // 动画结束，重新开始
                gif.reset();
                ESP_LOGI(TAG, "Animation loop completed, restarting...");
            } else if (result < 0) {
                // 发生错误
                ESP_LOGE(TAG, "Error playing frame: %d", gif.getLastError());
                break;
            }
            
            // 等待帧延迟时间
            if (frameDelay > 0) {
                vTaskDelay(pdMS_TO_TICKS(frameDelay));
            }
        }
        
        gif.close();
    } else {
        ESP_LOGE(TAG, "Failed to open GIF file: %s, error: %d", filename, gif.getLastError());
    }
}

// 主任务示例
void gif_player_task(void *pvParameters)
{
    ESP_LOGI(TAG, "GIF Player task started");
    
    // 示例1：播放内存中的GIF（需要提供实际的GIF数据）
    // play_gif_from_memory(gif_data, gif_size);
    
    // 示例2：播放文件中的GIF
    // play_gif_from_file("/spiffs/animation.gif");
    
    ESP_LOGI(TAG, "GIF Player task completed");
    vTaskDelete(NULL);
}

// 在app_main()中调用此函数来启动GIF播放任务
void start_gif_player(void)
{
    xTaskCreate(gif_player_task, "gif_player", 8192, NULL, 5, NULL);
}
