//
// GIF Player for ESP-IDF
// A helper class to play GIF Animations on ESP32 MCUs using ESP-IDF
// Adapted from original GIFPlayer by Larry <PERSON> (<EMAIL>)
//
// This version removes Arduino-specific dependencies and adapts for ESP-IDF
// 
// Copyright 2025 BitBank Software, Inc. All Rights Reserved.
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//    http://www.apache.org/licenses/LICENSE-2.0
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//===========================================================================
#ifndef __GIFPLAYER_ESP32__
#define __GIFPLAYER_ESP32__

#include "AnimatedGIF.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#ifdef ESP_PLATFORM
#include "esp_heap_caps.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#endif

// To ask the player to center the frame
#define GIF_CENTER -2

// Forward declarations for display interface
typedef struct {
    int width;
    int height;
    void (*setAddrWindow)(int x, int y, int w, int h);
    void (*pushPixels)(uint16_t *pixels, int count);
} esp_display_t;

static esp_display_t *_pDisplay = NULL;
static int _x, _y;

class GIFPlayer_ESP32
{
  public:
    // Open GIF from memory data
    int openData(esp_display_t *pDisplay, const void *pData, int iDataSize);
    
    // Open GIF from file (requires custom file read functions)
    int openFile(esp_display_t *pDisplay, const char *filename, 
                 GIF_OPEN_CALLBACK *pfnOpen, GIF_CLOSE_CALLBACK *pfnClose, 
                 GIF_READ_CALLBACK *pfnRead, GIF_SEEK_CALLBACK *pfnSeek);
    
    // Get GIF information
    int getInfo(int *width, int *height);
    
    // Play one frame
    int play(int x, int y, bool bDelay);
    
    // Close and cleanup
    int close();
    
    // Get the underlying AnimatedGIF object for advanced usage
    AnimatedGIF* getGIF() { return &_gif; }
    
protected:
    AnimatedGIF _gif;
    void *_pFrameBuffer;
};

// Implementation
int GIFPlayer_ESP32::openData(esp_display_t *pDisplay, const void *pData, int iDataSize)
{
    void *pBuf;
    _gif.begin(GIF_PALETTE_RGB565_BE);
    if (_gif.open((uint8_t *)pData, iDataSize, NULL)) {
        int canvasSize = _gif.getCanvasWidth() * _gif.getCanvasHeight() * 3;
#ifdef ESP_PLATFORM
        pBuf = heap_caps_malloc(canvasSize, MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT);
        if (!pBuf) {
            pBuf = heap_caps_malloc(canvasSize, MALLOC_CAP_INTERNAL | MALLOC_CAP_8BIT);
        }
#else
        pBuf = malloc(canvasSize);
#endif
        if (!pBuf) {
            return GIF_ERROR_MEMORY;
        }
        _pFrameBuffer = pBuf;
        _gif.setFrameBuf(pBuf);
        _gif.setDrawType(GIF_DRAW_COOKED);
    } else {
        return GIF_FILE_NOT_OPEN;
    }
    _pDisplay = pDisplay;
    return GIF_SUCCESS;
}

int GIFPlayer_ESP32::openFile(esp_display_t *pDisplay, const char *filename,
                              GIF_OPEN_CALLBACK *pfnOpen, GIF_CLOSE_CALLBACK *pfnClose,
                              GIF_READ_CALLBACK *pfnRead, GIF_SEEK_CALLBACK *pfnSeek)
{
    void *pBuf;
    _gif.begin(GIF_PALETTE_RGB565_BE);
    if (_gif.open(filename, pfnOpen, pfnClose, pfnRead, pfnSeek, NULL)) {
        int canvasSize = _gif.getCanvasWidth() * _gif.getCanvasHeight() * 3;
#ifdef ESP_PLATFORM
        pBuf = heap_caps_malloc(canvasSize, MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT);
        if (!pBuf) {
            pBuf = heap_caps_malloc(canvasSize, MALLOC_CAP_INTERNAL | MALLOC_CAP_8BIT);
        }
#else
        pBuf = malloc(canvasSize);
#endif
        if (!pBuf) {
            _gif.close();
            return GIF_ERROR_MEMORY;
        }
        _pFrameBuffer = pBuf;
        _gif.setFrameBuf(pBuf);
        _gif.setDrawType(GIF_DRAW_COOKED);
    } else {
        return GIF_FILE_NOT_OPEN;
    }
    _pDisplay = pDisplay;
    return GIF_SUCCESS;
}

int GIFPlayer_ESP32::getInfo(int *width, int *height)
{
    if (_pDisplay) { // initialized successfully?
        if (width) *width = _gif.getCanvasWidth();
        if (height) *height = _gif.getCanvasHeight();
        return GIF_SUCCESS;
    } else {
        return GIF_INVALID_PARAMETER;
    }
}

int GIFPlayer_ESP32::play(int x, int y, bool bDelay)
{
    int rc, ty, cw, ch, w, h;
    int iX, iY;
    uint16_t *pPixels;
    
    if (!_pDisplay) {
        return GIF_INVALID_PARAMETER;
    }
    
    if (x == GIF_CENTER) {
        x = (_pDisplay->width - _gif.getCanvasWidth())/2;
        if (x < 0) x = 0;
    }
    if (y == GIF_CENTER) {
        y = (_pDisplay->height - _gif.getCanvasHeight())/2;
        if (y < 0) y = 0;
    }
    _x = x; _y = y;
    cw = _gif.getCanvasWidth();
    ch = _gif.getCanvasHeight();
    w = _gif.getFrameWidth();
    h = _gif.getFrameHeight();
    iX = _gif.getFrameXOff();
    iY = _gif.getFrameYOff();
    rc = _gif.playFrame(bDelay, NULL);
    if (!rc) _gif.reset(); // loop forever
    
    // Update the display with the new pixels
    if (_pDisplay->setAddrWindow && _pDisplay->pushPixels) {
        _pDisplay->setAddrWindow(_x + iX, _y + iY, w, h);
        pPixels = (uint16_t *)(_gif.getFrameBuf() + (cw * ch)); // cooked pixels start here
        pPixels += iX + (iY * cw);
        // the frame is a sub-region of the canvas, so we can't push
        // the pixels in one shot
        for (ty = 0; ty < h; ty++) {
            _pDisplay->pushPixels(pPixels, w);
            pPixels += cw; // canvas width to the next line
        }
    }
    return rc;
}

int GIFPlayer_ESP32::close()
{
    if (_pFrameBuffer) {
#ifdef ESP_PLATFORM
        heap_caps_free(_pFrameBuffer);
#else
        free(_pFrameBuffer);
#endif
        _pFrameBuffer = NULL;
    }
    _gif.close();
    _pDisplay = NULL;
    return GIF_SUCCESS;
}

#endif // __GIFPLAYER_ESP32__
