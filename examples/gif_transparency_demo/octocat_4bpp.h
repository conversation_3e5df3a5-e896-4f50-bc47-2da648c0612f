//
// octocat_4bpp
// Data size = 1190 bytes
//
// GIF, Compression=LZW, Size: 120 x 100, 5-Bpp
// 0 frames
//
// for non-Arduino builds...
#ifndef PROGMEM
#define PROGMEM
#endif
const uint8_t octocat_4bpp[] PROGMEM = {
	0x47,0x49,0x46,0x38,0x39,0x61,0x78,0x00,0x64,0x00,0x84,0x10,0x00,0x2f,0x41,0x70,
	0x00,0x00,0x00,0x2a,0x2c,0x2c,0x59,0x49,0x41,0x46,0x5f,0x6a,0x7c,0x65,0x58,0xad,
	0x5c,0x53,0xb5,0x70,0x67,0xa0,0x80,0x71,0x6c,0x99,0xac,0xc9,0xa1,0x8f,0x83,0xb7,
	0xc9,0x7b,0xbb,0xe4,0xdc,0xc5,0xc3,0x9b,0xd8,0xf0,0xf4,0xc9,0xb3,0xff,0xff,0xff,
	0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
	0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
	0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0x21,0xf9,0x04,
	0x01,0x0a,0x00,0x1f,0x00,0x2c,0x00,0x00,0x00,0x00,0x78,0x00,0x64,0x00,0x00,0x05,
	0xfe,0xe0,0x27,0x8e,0x64,0x69,0x9e,0x68,0xaa,0xae,0x6c,0xeb,0xbe,0x70,0x2c,0xcf,
	0x74,0x6d,0xcf,0x42,0x2e,0xdc,0xb6,0x9e,0xf3,0x40,0x98,0xcf,0x17,0x6c,0x0d,0x75,
	0xc5,0xa4,0xea,0x48,0x54,0x96,0x98,0x48,0xa7,0x74,0x04,0x6d,0x2a,0xab,0xd6,0xe9,
	0x15,0x1b,0x25,0x71,0x8f,0xcf,0xef,0x4e,0xbb,0x15,0x9b,0xcf,0x62,0x72,0x19,0xcd,
	0x6e,0x77,0xd5,0x40,0xb7,0xbc,0x0d,0x2f,0xce,0xef,0xe6,0x7a,0x10,0xcf,0xc7,0xea,
	0xe3,0x7d,0x81,0x43,0x7f,0x80,0x82,0x82,0x84,0x3c,0x86,0x86,0x88,0x37,0x8a,0x87,
	0x8c,0x35,0x8e,0x8f,0x90,0x31,0x92,0x8b,0x94,0x2f,0x96,0x97,0x98,0x46,0x9a,0x93,
	0x9c,0x29,0x9e,0x9b,0xa0,0x28,0xa2,0xa3,0xa4,0x5e,0xa6,0x9f,0xa8,0x54,0xaa,0xab,
	0xac,0xae,0xaf,0xa4,0xb1,0xb2,0xa0,0xb4,0x81,0xac,0xa9,0x67,0x03,0x05,0x08,0xbe,
	0xbe,0x05,0xc1,0x03,0xc3,0x4c,0xc3,0xbc,0xc1,0xbf,0xc0,0x05,0x03,0x68,0xb9,0x22,
	0x66,0xbd,0x0a,0xd2,0xd3,0xd4,0xd5,0xd6,0xd7,0xd7,0x08,0x05,0x79,0xce,0x62,0x08,
	0x0a,0x0f,0xe1,0xe2,0xe3,0xe4,0xe5,0xe6,0xe7,0x0f,0x0a,0x08,0xcc,0x5c,0xce,0x1f,
	0x5f,0x08,0xe8,0xf2,0xf3,0xf3,0x0a,0xec,0x55,0xee,0x5c,0xf1,0xf4,0xfc,0xfd,0xe2,
	0x08,0xed,0xba,0x55,0xd9,0x47,0x0e,0x82,0x41,0x08,0xfe,0xd0,0x1d,0x44,0x48,0x0e,
	0x20,0x3e,0x81,0xc5,0xc0,0x15,0x5c,0xc8,0x30,0xe1,0x38,0x8a,0x15,0xc5,0x6d,0x83,
	0x92,0x0f,0x0a,0x41,0x71,0x07,0x1b,0x34,0x38,0x68,0x11,0x24,0x04,0x91,0x0b,0xfe,
	0xc9,0x29,0x78,0x98,0x0b,0xca,0x00,0x89,0xe2,0x46,0x9e,0x3c,0x60,0xc0,0xc0,0xc8,
	0x06,0x25,0x6f,0xd6,0x3c,0x20,0x13,0xe7,0xb8,0x7b,0x83,0x20,0x0e,0x29,0x50,0xce,
	0xa0,0xc8,0x06,0x35,0x6d,0x66,0xec,0x77,0x52,0x41,0x03,0x9a,0x07,0x48,0x8e,0x73,
	0x18,0x54,0xa8,0x8f,0x8f,0xe1,0x0c,0x2a,0x30,0x48,0xd3,0xc0,0xd6,0x92,0x10,0x9c,
	0x86,0xad,0x29,0x53,0x25,0x13,0x77,0xcf,0x8e,0xc0,0x34,0x79,0x40,0x5a,0xd2,0xa8,
	0x60,0x69,0x22,0x48,0x8a,0xc0,0xa0,0x59,0x30,0x68,0x99,0xac,0xcd,0x0a,0xa1,0xeb,
	0xdb,0xa5,0xfc,0xfa,0x26,0xdd,0x69,0x77,0xdc,0x4a,0xbc,0x1d,0x87,0x60,0x7d,0xc0,
	0x75,0xb0,0x01,0xb8,0x16,0x05,0x0f,0x8e,0xba,0xf4,0x70,0x55,0xab,0x39,0x16,0x6b,
	0x75,0xfc,0x35,0xb2,0x02,0xce,0x85,0xff,0x9d,0x45,0xfb,0x6e,0x68,0xd1,0x93,0x8e,
	0x47,0x82,0x45,0x3a,0xb8,0xec,0xd4,0xd1,0x79,0x87,0x0c,0x28,0x27,0xf3,0x33,0xd9,
	0x93,0x39,0xc7,0xde,0x06,0xbc,0x31,0x4b,0x62,0x1f,0x7b,0x19,0x1b,0x45,0x09,0x98,
	0xe9,0x49,0xe2,0x80,0x61,0x93,0x3e,0x42,0xf4,0x74,0xca,0x73,0x9f,0x83,0x5f,0xa4,
	0x58,0xce,0xf2,0x1b,0xd2,0xa5,0x81,0x9b,0x7b,0x0e,0xfd,0xf1,0x3c,0xee,0xe3,0x7a,
	0x5f,0xc7,0x6e,0xba,0xa4,0x38,0xe9,0xfe,0xac,0xff,0xc0,0x1e,0xe6,0xaa,0xf9,0xf7,
	0xe4,0x80,0x8e,0x61,0xdf,0x5e,0xc7,0xe2,0x7a,0x30,0xa5,0x95,0x14,0x3f,0x9f,0xbe,
	0x89,0xa1,0xe8,0x9d,0xd3,0x56,0x3a,0x07,0xec,0x77,0x99,0x7f,0xff,0x29,0xfe,0x16,
	0x60,0x75,0xfa,0x25,0x64,0xcf,0x81,0x08,0x26,0x08,0x20,0x7c,0xe8,0xa8,0x23,0x5f,
	0x7f,0x11,0x86,0x52,0xcc,0x37,0x0d,0x5a,0x34,0xcd,0x3a,0xca,0xd1,0x37,0x5e,0x29,
	0x58,0x0c,0x13,0xcc,0x89,0x28,0xa2,0x48,0x0c,0x4b,0x11,0xfa,0x40,0x00,0x01,0x99,
	0x48,0x92,0xa1,0x84,0x2f,0xae,0x57,0x49,0x00,0x38,0xe2,0x08,0x45,0x8e,0x3a,0xce,
	0xd8,0x89,0x00,0x0b,0x24,0x80,0x61,0x0b,0x3c,0x16,0x69,0x64,0x91,0x3e,0xaa,0xe0,
	0x40,0x5a,0x23,0xaa,0x10,0x00,0x1b,0x3c,0x26,0x89,0xc2,0x92,0x4c,0x2e,0x00,0xa3,
	0x0b,0x77,0xe0,0x28,0x65,0x09,0x0e,0x50,0x99,0x56,0x02,0x04,0x0c,0x49,0xe2,0x1c,
	0x01,0x6c,0xb9,0x82,0x0e,0x09,0xfc,0x98,0xa5,0x99,0x1a,0x02,0x29,0xe4,0x99,0x7c,
	0x94,0xc9,0xe6,0x98,0x02,0xbc,0x48,0xa7,0x1f,0x69,0xcc,0x79,0x82,0x0f,0x09,0x2c,
	0x50,0x67,0x98,0x75,0xe6,0x60,0xe5,0x17,0x4c,0xb2,0xa8,0xa7,0x2e,0x81,0x26,0xe0,
	0xc0,0x8b,0x0e,0x08,0x09,0x28,0x11,0x78,0x0d,0x82,0xd8,0xa1,0xf5,0x55,0xf1,0x24,
	0xa4,0x41,0x65,0xea,0x1b,0xa5,0xad,0x60,0x71,0x29,0x12,0x91,0x36,0x01,0x21,0xa7,
	0x9d,0x32,0xf1,0xe9,0x0f,0xa1,0x46,0x31,0x2a,0xa9,0x88,0x8e,0x01,0x46,0xaa,0xeb,
	0xad,0xca,0x6a,0x9b,0xa0,0x6a,0xaa,0xea,0xa6,0xb3,0x2e,0x21,0xa9,0xad,0xb1,0xe2,
	0x9a,0xeb,0x9d,0x3b,0xc0,0xea,0xaa,0xaf,0xbf,0x4a,0x58,0xab,0xa8,0xc8,0x36,0x59,
	0x6c,0xa5,0xc1,0xf2,0x3a,0xac,0xb2,0xcb,0x96,0x8a,0xea,0x20,0x8f,0xfe,0xf6,0x0a,
	0x6d,0xb4,0xd9,0xe5,0x40,0x40,0xa3,0x43,0x04,0xd9,0xad,0x03,0xb2,0x46,0xdb,0xa5,
	0x90,0x68,0x72,0xeb,0x83,0xb9,0x3a,0x2c,0x00,0xae,0x0f,0xea,0x76,0x89,0x6d,0x97,
	0xf0,0xa2,0x2b,0x80,0xa2,0xe4,0xea,0x20,0x2f,0x90,0xeb,0xa6,0x1b,0x2f,0xbc,0xac,
	0xee,0x1b,0x6f,0xbd,0xf3,0xde,0x7b,0xaf,0xba,0xdf,0xfa,0xcb,0xef,0x9c,0x06,0xff,
	0xcb,0xa7,0xc0,0x00,0xe3,0x5b,0x70,0xc2,0xee,0x4a,0x09,0x71,0xbc,0xd5,0xd2,0x3b,
	0xc4,0xc0,0x8b,0xba,0x38,0x71,0xbc,0x33,0x6e,0x0c,0x6f,0xc3,0xea,0x36,0x8c,0x31,
	0xc8,0x1e,0x47,0xec,0x5f,0xc9,0xf7,0x6e,0x3b,0x30,0xc9,0x19,0xeb,0x40,0x40,0xbb,
	0x1e,0x23,0xe8,0xf1,0xa0,0x1a,0x8f,0x7b,0x31,0xcb,0x34,0x97,0x5b,0x32,0x7d,0x25,
	0x57,0x4b,0x80,0xa2,0x1f,0x6b,0xec,0xe7,0xc2,0x1f,0x57,0x0b,0xf3,0xc6,0xec,0xa1,
	0x9c,0xc0,0xd2,0x0c,0x34,0xcd,0x00,0xbc,0x15,0x77,0xe9,0x33,0xbc,0x4e,0x33,0xb0,
	0xf4,0xd1,0x48,0x63,0x87,0x72,0x97,0x55,0x37,0x2d,0xaf,0xca,0x0e,0x0c,0xad,0x6f,
	0xd7,0x4f,0x6f,0xed,0x25,0x5a,0x66,0x93,0xed,0xf3,0xd1,0x0d,0x27,0xd0,0xb5,0xd9,
	0x67,0x93,0x96,0x76,0xd5,0x80,0xfe,0xec,0x35,0xd5,0x0c,0x3c,0xea,0x76,0xd5,0x70,
	0x9f,0xbc,0x35,0xd9,0x4c,0x3b,0x8d,0xb7,0xd3,0x7d,0xbe,0x6d,0x76,0x86,0x28,0x93,
	0xed,0x80,0xe2,0x8b,0xbf,0x6d,0x38,0xca,0x3e,0x96,0xac,0x38,0xe3,0x86,0x3f,0x1e,
	0xb3,0xc4,0x13,0x4f,0x5e,0x39,0xdf,0x8d,0x0b,0x7e,0x39,0x9b,0x27,0x10,0x6b,0xce,
	0xf9,0xe6,0x9e,0x27,0x3c,0xab,0xbf,0xa2,0x7b,0x4e,0xfa,0xdd,0xfb,0x62,0xfb,0x01,
	0xd7,0x8e,0xaf,0x5e,0xf6,0xe3,0xae,0xaf,0xf0,0x34,0xe5,0xa3,0x33,0x50,0xfb,0xee,
	0xbc,0x4f,0x11,0x02,0x00,0x3b};
