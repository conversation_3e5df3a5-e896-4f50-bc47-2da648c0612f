# ESP32-LGFX-SDCard-GifPlayer.ino

GIF player Demo for M5Stack, Odroid-GO, ESP32-Wrover-Kit, LoLinD32-Pro, D-Duino32-XS, and more...

Depends on the following libraries:

  - [ESP32-Chimera-core](https://github.com/tobozo/ESP32-Chimera-core)
  - [LovyanGFX](https://github.com/lovyan03/LovyanGFX)

This sketch will open the SD card and queue all files found in the `/gif/` folder then play them in an endless loop.

loosely coded by @tobozo for @bitbank2
