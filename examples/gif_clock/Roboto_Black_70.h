// Created by http://oleddisplay.squix.ch/ Consider a donation
// In case of problems make sure that you are using the font file with the correct version!
const uint8_t <PERSON>o_Black_70Bitmaps[] PROGMEM = {

	// Bitmap Data:
	0x00, // ' '
	0x7F,0xF9,0xFF,0xE7,0xFF,0x9F,0xFE,0x7F,0xF9,0xFF,0xE7,0xFF,0x9F,0xFE,0x7F,0xF9,0xFF,0xE7,0xFF,0x9F,0xFE,0x7F,0xF9,0xFF,0xC7,0xFF,0x1F,0xFC,0x7F,0xF1,0xFF,0xC3,0xFF,0x0F,0xFC,0x3F,0xF0,0xFF,0xC3,0xFF,0x0F,0xFC,0x3F,0xF0,0xFF,0xC3,0xFF,0x0F,0xFC,0x3F,0xF0,0xFF,0xC3,0xFF,0x0F,0xFC,0x3F,0xF0,0xFF,0xC3,0xFF,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0F,0xC0,0xFF,0x87,0xFF,0x1F,0xFE,0xFF,0xFB,0xFF,0xEF,0xFF,0x9F,0xFE,0x7F,0xF0,0xFF,0x80,0xFC,0x00, // '!'
	0xFF,0x0F,0xF7,0xF8,0x7F,0xBF,0xC3,0xFD,0xFE,0x1F,0xEF,0xF0,0xFF,0x7F,0x87,0xFB,0xF8,0x3F,0x9F,0xC1,0xFC,0xFE,0x0F,0xE7,0xF0,0x7F,0x3F,0x83,0xF9,0xFC,0x1F,0xCF,0xE0,0xFE,0x7F,0x07,0xF3,0xF8,0x3F,0x9F,0xC1,0xFC,0xFE,0x0F,0xE7,0xE0,0x7E,0x3F,0x03,0xF1,0xF8,0x1F,0x80, // '"'
	0x00,0x03,0xF0,0x3F,0x80,0x00,0x0F,0xE0,0x7F,0x00,0x00,0x1F,0xC0,0xFC,0x00,0x00,0x3F,0x83,0xF8,0x00,0x00,0x7F,0x07,0xF0,0x00,0x00,0xFE,0x0F,0xE0,0x00,0x01,0xF8,0x1F,0xC0,0x00,0x07,0xF0,0x3F,0x80,0x00,0x0F,0xE0,0x7E,0x00,0x00,0x1F,0xC1,0xFC,0x00,0x00,0x3F,0x83,0xF8,0x00,0x00,0x7F,0x07,0xF0,0x00,0x00,0xFE,0x0F,0xE0,0x00,0x01,0xF8,0x1F,0xC0,0x00,0x07,0xF0,0x3F,0x80,0x0F,0xFF,0xFF,0xFF,0xFE,0x1F,0xFF,0xFF,0xFF,0xFC,0x3F,0xFF,0xFF,0xFF,0xF8,0x7F,0xFF,0xFF,0xFF,0xF0,0xFF,0xFF,0xFF,0xFF,0xE1,0xFF,0xFF,0xFF,0xFF,0xC0,0x07,0xF0,0x3F,0x00,0x00,0x0F,0xE0,0xFE,0x00,0x00,0x1F,0xC1,0xFC,0x00,0x00,0x3F,0x83,0xF8,0x00,0x00,0x7E,0x07,0xF0,0x00,0x01,0xFC,0x0F,0xE0,0x00,0x03,0xF8,0x3F,0x80,0x00,0x07,0xF0,0x7F,0x00,0x00,0x0F,0xE0,0xFE,0x00,0x3F,0xFF,0xFF,0xFF,0xFC,0x7F,0xFF,0xFF,0xFF,0xF8,0xFF,0xFF,0xFF,0xFF,0xF1,0xFF,0xFF,0xFF,0xFF,0xE3,0xFF,0xFF,0xFF,0xFF,0xC7,0xFF,0xFF,0xFF,0xFF,0x80,0x0F,0xE0,0xFE,0x00,0x00,0x1F,0x81,0xFC,0x00,0x00,0x7F,0x03,0xF0,0x00,0x00,0xFE,0x0F,0xE0,0x00,0x01,0xFC,0x1F,0xC0,0x00,0x03,0xF8,0x3F,0x80,0x00,0x07,0xF0,0x7F,0x00,0x00,0x0F,0xC0,0xFE,0x00,0x00,0x3F,0x81,0xFC,0x00,0x00,0x7F,0x03,0xF0,0x00,0x00,0xFE,0x0F,0xE0,0x00,0x01,0xFC,0x1F,0xC0,0x00,0x03,0xF8,0x3F,0x80,0x00,0x0F,0xE0,0x7F,0x00,0x00, // '#'
	0x00,0x00,0xFC,0x00,0x00,0x00,0x0F,0xC0,0x00,0x00,0x00,0xFC,0x00,0x00,0x00,0x0F,0xC0,0x00,0x00,0x00,0xFC,0x00,0x00,0x00,0x0F,0xC0,0x00,0x00,0x00,0xFC,0x00,0x00,0x00,0x7F,0xE0,0x00,0x00,0x3F,0xFF,0xC0,0x00,0x0F,0xFF,0xFF,0x00,0x01,0xFF,0xFF,0xFC,0x00,0x3F,0xFF,0xFF,0xE0,0x07,0xFF,0xFF,0xFF,0x00,0xFF,0xFF,0xFF,0xF0,0x1F,0xFF,0xFF,0xFF,0x81,0xFF,0xFF,0xFF,0xF8,0x1F,0xFF,0x0F,0xFF,0xC3,0xFF,0xE0,0x3F,0xFC,0x3F,0xFC,0x03,0xFF,0xC3,0xFF,0xC0,0x1F,0xFE,0x3F,0xF8,0x01,0xFF,0xE3,0xFF,0x80,0x1F,0xFE,0x3F,0xFC,0x01,0xFF,0xE3,0xFF,0xC0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x03,0xFF,0xE0,0x00,0x00,0x1F,0xFF,0x00,0x00,0x01,0xFF,0xFC,0x00,0x00,0x0F,0xFF,0xF0,0x00,0x00,0x7F,0xFF,0xE0,0x00,0x03,0xFF,0xFF,0x80,0x00,0x1F,0xFF,0xFE,0x00,0x00,0xFF,0xFF,0xF0,0x00,0x07,0xFF,0xFF,0xC0,0x00,0x1F,0xFF,0xFE,0x00,0x00,0x3F,0xFF,0xF0,0x00,0x00,0xFF,0xFF,0x00,0x00,0x03,0xFF,0xF8,0x00,0x00,0x0F,0xFF,0xC0,0x00,0x00,0x7F,0xFC,0x00,0x00,0x03,0xFF,0xCF,0xFF,0x00,0x1F,0xFC,0xFF,0xF0,0x01,0xFF,0xEF,0xFF,0x00,0x1F,0xFE,0xFF,0xF0,0x01,0xFF,0xEF,0xFF,0x00,0x1F,0xFE,0x7F,0xF8,0x01,0xFF,0xE7,0xFF,0xC0,0x3F,0xFC,0x7F,0xFE,0x07,0xFF,0xC3,0xFF,0xFF,0xFF,0xFC,0x3F,0xFF,0xFF,0xFF,0x81,0xFF,0xFF,0xFF,0xF8,0x0F,0xFF,0xFF,0xFF,0x00,0x7F,0xFF,0xFF,0xE0,0x03,0xFF,0xFF,0xFC,0x00,0x1F,0xFF,0xFF,0x80,0x00,0x7F,0xFF,0xE0,0x00,0x00,0x7F,0xF0,0x00,0x00,0x01,0xF8,0x00,0x00,0x00,0x1F,0x80,0x00,0x00,0x01,0xF8,0x00,0x00,0x00,0x1F,0x80,0x00,0x00,0x01,0xF8,0x00,0x00,0x00,0x1F,0x80,0x00,0x00,0x01,0xF8,0x00,0x00, // '$'
	0x01,0xFE,0x00,0x00,0x00,0x00,0x0F,0xFF,0x00,0x00,0x00,0x00,0x7F,0xFF,0x80,0x00,0x00,0x01,0xFF,0xFF,0x80,0x00,0x00,0x03,0xFF,0xFF,0x80,0x00,0x00,0x0F,0xFF,0xFF,0x00,0x10,0x00,0x1F,0xE1,0xFF,0x00,0x78,0x00,0x7F,0x81,0xFE,0x00,0xFC,0x00,0xFF,0x01,0xFC,0x03,0xFC,0x01,0xFE,0x03,0xF8,0x0F,0xF0,0x03,0xFC,0x07,0xF0,0x1F,0xC0,0x07,0xF8,0x0F,0xE0,0x7F,0x00,0x0F,0xF0,0x1F,0xC0,0xFE,0x00,0x1F,0xE0,0x3F,0x83,0xF8,0x00,0x3F,0xC0,0x7F,0x0F,0xE0,0x00,0x7F,0x81,0xFE,0x1F,0xC0,0x00,0x7F,0x87,0xFC,0x7F,0x00,0x00,0xFF,0xFF,0xF1,0xFC,0x00,0x00,0xFF,0xFF,0xE3,0xF8,0x00,0x01,0xFF,0xFF,0x8F,0xE0,0x00,0x01,0xFF,0xFE,0x1F,0xC0,0x00,0x00,0xFF,0xF8,0x7F,0x00,0x00,0x00,0x7F,0x81,0xFC,0x00,0x00,0x00,0x00,0x03,0xF8,0x00,0x00,0x00,0x00,0x0F,0xE0,0x00,0x00,0x00,0x00,0x3F,0x80,0x00,0x00,0x00,0x00,0x7F,0x00,0x00,0x00,0x00,0x01,0xFC,0x00,0x00,0x00,0x00,0x07,0xF8,0x1F,0xE0,0x00,0x00,0x0F,0xE0,0xFF,0xF0,0x00,0x00,0x3F,0x87,0xFF,0xF8,0x00,0x00,0x7F,0x1F,0xFF,0xF8,0x00,0x01,0xFC,0x7F,0xFF,0xF0,0x00,0x07,0xF0,0xFF,0xFF,0xF0,0x00,0x0F,0xE3,0xFE,0x1F,0xE0,0x00,0x3F,0x87,0xF8,0x1F,0xE0,0x00,0xFE,0x0F,0xE0,0x3F,0xC0,0x01,0xFC,0x1F,0xC0,0x7F,0x80,0x07,0xF0,0x3F,0x80,0xFF,0x00,0x1F,0xE0,0x7F,0x01,0xFE,0x00,0x3F,0x80,0xFE,0x03,0xFC,0x00,0xFE,0x01,0xFC,0x07,0xF8,0x01,0xFC,0x03,0xF8,0x0F,0xF0,0x07,0xF0,0x07,0xF8,0x1F,0xE0,0x03,0xC0,0x0F,0xF8,0x7F,0x80,0x01,0x80,0x0F,0xFF,0xFF,0x00,0x00,0x00,0x1F,0xFF,0xFC,0x00,0x00,0x00,0x1F,0xFF,0xF8,0x00,0x00,0x00,0x1F,0xFF,0xE0,0x00,0x00,0x00,0x0F,0xFF,0x00,0x00,0x00,0x00,0x07,0xF8,0x00, // '%'
	0x00,0x03,0xFC,0x00,0x00,0x00,0x00,0xFF,0xFC,0x00,0x00,0x00,0x0F,0xFF,0xF8,0x00,0x00,0x01,0xFF,0xFF,0xE0,0x00,0x00,0x1F,0xFF,0xFF,0x80,0x00,0x00,0xFF,0xFF,0xFE,0x00,0x00,0x0F,0xFF,0xFF,0xF0,0x00,0x00,0xFF,0xFF,0xFF,0xC0,0x00,0x07,0xFF,0xFF,0xFE,0x00,0x00,0x3F,0xF8,0x7F,0xF0,0x00,0x01,0xFF,0x81,0xFF,0x80,0x00,0x1F,0xFC,0x07,0xFC,0x00,0x00,0xFF,0xC0,0x3F,0xE0,0x00,0x07,0xFE,0x01,0xFF,0x00,0x00,0x3F,0xF0,0x0F,0xF8,0x00,0x01,0xFF,0xC0,0xFF,0xC0,0x00,0x07,0xFE,0x07,0xFC,0x00,0x00,0x3F,0xF8,0xFF,0xE0,0x00,0x01,0xFF,0xCF,0xFE,0x00,0x00,0x07,0xFF,0xFF,0xF0,0x00,0x00,0x3F,0xFF,0xFF,0x00,0x00,0x00,0xFF,0xFF,0xF0,0x00,0x00,0x07,0xFF,0xFE,0x00,0x00,0x00,0x1F,0xFF,0xE0,0x00,0x00,0x00,0xFF,0xFC,0x00,0x00,0x00,0x0F,0xFF,0xE0,0x0F,0xFC,0x00,0xFF,0xFF,0x80,0x7F,0xE0,0x1F,0xFF,0xFE,0x03,0xFF,0x01,0xFF,0xFF,0xF8,0x1F,0xF8,0x0F,0xFF,0xFF,0xE0,0xFF,0xC0,0xFF,0xFF,0xFF,0x87,0xFE,0x0F,0xFF,0x3F,0xFE,0x7F,0xF0,0x7F,0xF1,0xFF,0xF3,0xFF,0x07,0xFF,0x87,0xFF,0xDF,0xF8,0x3F,0xFC,0x1F,0xFF,0xFF,0xC1,0xFF,0xC0,0x7F,0xFF,0xFE,0x0F,0xFE,0x01,0xFF,0xFF,0xE0,0x7F,0xF8,0x07,0xFF,0xFF,0x03,0xFF,0xC0,0x1F,0xFF,0xF0,0x1F,0xFE,0x00,0x7F,0xFF,0x80,0xFF,0xF8,0x01,0xFF,0xF8,0x03,0xFF,0xF0,0x7F,0xFF,0x80,0x1F,0xFF,0xFF,0xFF,0xFE,0x00,0xFF,0xFF,0xFF,0xFF,0xF8,0x03,0xFF,0xFF,0xFF,0xFF,0xE0,0x0F,0xFF,0xFF,0xFF,0xFF,0x80,0x3F,0xFF,0xFF,0xFF,0xFE,0x00,0xFF,0xFF,0xFF,0xFF,0xF8,0x03,0xFF,0xFF,0xE3,0xFF,0xE0,0x07,0xFF,0xFC,0x0F,0xFF,0x80,0x07,0xFF,0x00,0x00,0x00, // '&'
	0xFF,0x7F,0xBF,0xDF,0xEF,0xF7,0xFB,0xFD,0xFE,0xFF,0x7F,0xBF,0x9F,0xCF,0xE7,0xF3,0xF9,0xFC,0xFE,0x7F,0x3F,0x9F,0xC0, // '''
	0x00,0x00,0xC0,0x00,0x1E,0x00,0x01,0xF0,0x00,0x1F,0xC0,0x01,0xFE,0x00,0x1F,0xF0,0x01,0xFF,0x40,0x1F,0xF0,0x01,0xFF,0x80,0x0F,0xF8,0x00,0xFF,0x80,0x07,0xF8,0x00,0x7F,0xC0,0x03,0xFC,0x00,0x3F,0xE0,0x01,0xFF,0x00,0x1F,0xF0,0x00,0xFF,0x80,0x07,0xF8,0x00,0x7F,0xC0,0x03,0xFE,0x00,0x1F,0xF0,0x00,0xFF,0x80,0x0F,0xF8,0x00,0x7F,0xC0,0x03,0xFE,0x00,0x1F,0xF0,0x00,0xFF,0x80,0x07,0xFC,0x00,0x7F,0xE0,0x03,0xFF,0x00,0x1F,0xF0,0x00,0xFF,0x80,0x07,0xFC,0x00,0x3F,0xE0,0x01,0xFF,0x00,0x0F,0xF8,0x00,0x7F,0xC0,0x03,0xFE,0x00,0x1F,0xF8,0x00,0xFF,0xC0,0x03,0xFE,0x00,0x1F,0xF0,0x00,0xFF,0x80,0x07,0xFC,0x00,0x3F,0xE0,0x01,0xFF,0x00,0x07,0xF8,0x00,0x3F,0xE0,0x01,0xFF,0x00,0x0F,0xF8,0x00,0x3F,0xC0,0x01,0xFF,0x00,0x0F,0xF8,0x00,0x3F,0xC0,0x01,0xFF,0x00,0x07,0xF8,0x00,0x3F,0xE0,0x00,0xFF,0x00,0x07,0xFC,0x00,0x1F,0xF0,0x00,0xFF,0x80,0x03,0xFE,0x00,0x0F,0xF8,0x00,0x3F,0xF0,0x00,0xFF,0x00,0x03,0xF8,0x00,0x0F,0x80,0x00,0x3C,0x00,0x00,0x60, // '('
	0x20,0x00,0x03,0xC0,0x00,0x1F,0x00,0x01,0xFC,0x00,0x0F,0xF0,0x00,0x7F,0xC0,0x01,0xFF,0x00,0x07,0xFC,0x00,0x1F,0xF0,0x00,0xFF,0x80,0x03,0xFE,0x00,0x0F,0xF8,0x00,0x7F,0xC0,0x01,0xFF,0x00,0x0F,0xF8,0x00,0x7F,0xE0,0x01,0xFF,0x00,0x0F,0xFC,0x00,0x3F,0xE0,0x01,0xFF,0x00,0x0F,0xFC,0x00,0x7F,0xE0,0x03,0xFF,0x00,0x0F,0xF8,0x00,0x7F,0xE0,0x03,0xFF,0x00,0x1F,0xF8,0x00,0xFF,0xC0,0x07,0xFE,0x00,0x3F,0xF0,0x01,0xFF,0x80,0x0F,0xFC,0x00,0x3F,0xE0,0x01,0xFF,0x80,0x0F,0xFC,0x00,0x7F,0xE0,0x03,0xFF,0x00,0x1F,0xF8,0x01,0xFF,0x80,0x0F,0xFC,0x00,0x7F,0xE0,0x03,0xFF,0x00,0x1F,0xF8,0x00,0xFF,0xC0,0x07,0xFE,0x00,0x3F,0xF0,0x01,0xFF,0x00,0x1F,0xF8,0x00,0xFF,0xC0,0x07,0xFE,0x00,0x3F,0xE0,0x03,0xFF,0x00,0x1F,0xF8,0x00,0xFF,0x80,0x0F,0xFC,0x00,0x7F,0xC0,0x03,0xFE,0x00,0x3F,0xE0,0x01,0xFF,0x00,0x1F,0xF0,0x01,0xFF,0x80,0x1F,0xF8,0x00,0xFF,0x80,0x0F,0xF8,0x00,0xFF,0x80,0x07,0xF8,0x00,0x3F,0x80,0x00,0xF8,0x00,0x07,0x80,0x00,0x10,0x00,0x00, // ')'
	0x00,0x0F,0xE0,0x00,0x00,0x0F,0xE0,0x00,0x00,0x0F,0xE0,0x00,0x00,0x0F,0xE0,0x00,0x00,0x0F,0xE0,0x00,0x00,0x07,0xE0,0x00,0x00,0x07,0xE0,0x00,0x00,0x07,0xE0,0x00,0x30,0x07,0xE0,0x08,0x7C,0x07,0xE0,0x7C,0x7F,0x87,0xE1,0xFC,0x7F,0xE7,0xEF,0xFC,0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,0xFF,0xFE,0x0F,0xFF,0xFF,0xE0,0x00,0xFF,0xFF,0x00,0x00,0x1F,0xF0,0x00,0x00,0x3F,0xF8,0x00,0x00,0x7F,0xFC,0x00,0x00,0xFF,0xFE,0x00,0x00,0xFE,0xFE,0x00,0x01,0xFC,0x7F,0x00,0x03,0xFC,0x7F,0x80,0x07,0xF8,0x3F,0xC0,0x0F,0xF0,0x1F,0xE0,0x1F,0xF0,0x1F,0xF0,0x07,0xE0,0x0F,0xC0,0x03,0xC0,0x0F,0x80,0x00,0xC0,0x06,0x00,0x00,0x00,0x04,0x00, // '*'
	0x00,0x1F,0xFC,0x00,0x00,0x03,0xFF,0x80,0x00,0x00,0x7F,0xF0,0x00,0x00,0x0F,0xFE,0x00,0x00,0x01,0xFF,0xC0,0x00,0x00,0x3F,0xF8,0x00,0x00,0x07,0xFF,0x00,0x00,0x00,0xFF,0xE0,0x00,0x00,0x1F,0xFC,0x00,0x00,0x03,0xFF,0x80,0x00,0x00,0x7F,0xF0,0x00,0x00,0x0F,0xFE,0x00,0x00,0x01,0xFF,0xC0,0x01,0xFF,0xFF,0xFF,0xFF,0xBF,0xFF,0xFF,0xFF,0xF7,0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,0xFF,0xFF,0xDF,0xFF,0xFF,0xFF,0xFB,0xFF,0xFF,0xFF,0xFF,0x7F,0xFF,0xFF,0xFF,0xEF,0xFF,0xFF,0xFF,0xFD,0xFF,0xFF,0xFF,0xFF,0x80,0x07,0xFF,0x00,0x00,0x00,0xFF,0xE0,0x00,0x00,0x1F,0xFC,0x00,0x00,0x03,0xFF,0x80,0x00,0x00,0x7F,0xF0,0x00,0x00,0x0F,0xFE,0x00,0x00,0x01,0xFF,0xC0,0x00,0x00,0x3F,0xF8,0x00,0x00,0x07,0xFF,0x00,0x00,0x00,0xFF,0xE0,0x00,0x00,0x1F,0xFC,0x00,0x00,0x03,0xFF,0x80,0x00,0x00,0x7F,0xF0,0x00,0x00, // '+'
	0x1F,0xF0,0xFF,0x87,0xFC,0x3F,0xE1,0xFF,0x0F,0xF8,0x7F,0xC3,0xFE,0x1F,0xF0,0xFF,0x87,0xFC,0x3F,0xE1,0xFF,0x1F,0xF0,0xFF,0x8F,0xF8,0x7F,0xC7,0xFC,0x3F,0xE1,0xFE,0x03,0xE0,0x06,0x00, // ','
	0xFF,0xFF,0xFB,0xFF,0xFF,0xEF,0xFF,0xFF,0xBF,0xFF,0xFE,0xFF,0xFF,0xFB,0xFF,0xFF,0xEF,0xFF,0xFF,0xBF,0xFF,0xFE,0xFF,0xFF,0xF8, // '-'
	0x0F,0x80,0xFF,0x87,0xFF,0x3F,0xFC,0xFF,0xFB,0xFF,0xEF,0xFF,0xBF,0xFC,0x7F,0xF0,0xFF,0x80,0xF8,0x00, // '.'
	0x00,0x00,0x7F,0x80,0x00,0x1F,0xE0,0x00,0x0F,0xF0,0x00,0x03,0xFC,0x00,0x00,0xFF,0x00,0x00,0x7F,0x80,0x00,0x1F,0xE0,0x00,0x07,0xF8,0x00,0x03,0xFE,0x00,0x00,0xFF,0x00,0x00,0x3F,0xC0,0x00,0x1F,0xF0,0x00,0x07,0xF8,0x00,0x01,0xFE,0x00,0x00,0xFF,0x80,0x00,0x3F,0xC0,0x00,0x0F,0xF0,0x00,0x07,0xFC,0x00,0x01,0xFE,0x00,0x00,0x7F,0x80,0x00,0x1F,0xE0,0x00,0x0F,0xF0,0x00,0x03,0xFC,0x00,0x00,0xFF,0x00,0x00,0x7F,0x80,0x00,0x1F,0xE0,0x00,0x07,0xF8,0x00,0x03,0xFE,0x00,0x00,0xFF,0x00,0x00,0x3F,0xC0,0x00,0x1F,0xF0,0x00,0x07,0xF8,0x00,0x01,0xFE,0x00,0x00,0xFF,0x80,0x00,0x3F,0xC0,0x00,0x0F,0xF0,0x00,0x03,0xFC,0x00,0x01,0xFE,0x00,0x00,0x7F,0x80,0x00,0x1F,0xE0,0x00,0x0F,0xF0,0x00,0x03,0xFC,0x00,0x00,0xFF,0x00,0x00,0x7F,0xC0,0x00,0x1F,0xE0,0x00,0x07,0xF8,0x00,0x03,0xFE,0x00,0x00,0xFF,0x00,0x00,0x3F,0xC0,0x00,0x1F,0xF0,0x00,0x07,0xF8,0x00,0x01,0xFE,0x00,0x00,0xFF,0x80,0x00,0x3F,0xC0,0x00,0x00, // '/'
	0x00,0x07,0xFC,0x00,0x00,0x03,0xFF,0xF8,0x00,0x00,0xFF,0xFF,0xE0,0x00,0x1F,0xFF,0xFF,0x00,0x03,0xFF,0xFF,0xF8,0x00,0x7F,0xFF,0xFF,0xC0,0x0F,0xFF,0xFF,0xFE,0x01,0xFF,0xFF,0xFF,0xF0,0x1F,0xFF,0xFF,0xFF,0x03,0xFF,0xE0,0xFF,0xF8,0x3F,0xFC,0x07,0xFF,0x87,0xFF,0x80,0x3F,0xF8,0x7F,0xF8,0x03,0xFF,0xC7,0xFF,0x00,0x1F,0xFC,0x7F,0xF0,0x01,0xFF,0xC7,0xFF,0x00,0x1F,0xFC,0xFF,0xF0,0x01,0xFF,0xEF,0xFF,0x00,0x1F,0xFE,0xFF,0xF0,0x01,0xFF,0xEF,0xFF,0x00,0x1F,0xFE,0xFF,0xF0,0x01,0xFF,0xEF,0xFF,0x00,0x1F,0xFE,0xFF,0xF0,0x01,0xFF,0xEF,0xFF,0x00,0x1F,0xFE,0xFF,0xF0,0x01,0xFF,0xEF,0xFF,0x00,0x1F,0xFE,0xFF,0xF0,0x01,0xFF,0xEF,0xFF,0x00,0x1F,0xFE,0xFF,0xF0,0x01,0xFF,0xEF,0xFF,0x00,0x1F,0xFE,0xFF,0xF0,0x01,0xFF,0xEF,0xFF,0x00,0x1F,0xFE,0xFF,0xF0,0x01,0xFF,0xEF,0xFF,0x00,0x1F,0xFE,0xFF,0xF0,0x01,0xFF,0xE7,0xFF,0x00,0x1F,0xFC,0x7F,0xF0,0x01,0xFF,0xC7,0xFF,0x00,0x1F,0xFC,0x7F,0xF8,0x03,0xFF,0xC7,0xFF,0x80,0x3F,0xFC,0x3F,0xFC,0x07,0xFF,0x83,0xFF,0xE0,0xFF,0xF8,0x1F,0xFF,0xFF,0xFF,0x01,0xFF,0xFF,0xFF,0xF0,0x0F,0xFF,0xFF,0xFE,0x00,0x7F,0xFF,0xFF,0xC0,0x03,0xFF,0xFF,0xF8,0x00,0x1F,0xFF,0xFF,0x00,0x00,0xFF,0xFF,0xE0,0x00,0x03,0xFF,0xF8,0x00,0x00,0x07,0xFC,0x00,0x00, // '0'
	0x00,0x00,0x0E,0x00,0x00,0x7E,0x00,0x01,0xFE,0x00,0x0F,0xFE,0x00,0x7F,0xFE,0x03,0xFF,0xFE,0x0F,0xFF,0xFE,0x7F,0xFF,0xFE,0xFF,0xFF,0xFE,0xFF,0xFF,0xFE,0xFF,0xFF,0xFE,0xFF,0xFF,0xFE,0xFF,0xFF,0xFE,0xFF,0xFF,0xFE,0xFF,0x1F,0xFE,0xF8,0x1F,0xFE,0x80,0x1F,0xFE,0x00,0x1F,0xFE,0x00,0x1F,0xFE,0x00,0x1F,0xFE,0x00,0x1F,0xFE,0x00,0x1F,0xFE,0x00,0x1F,0xFE,0x00,0x1F,0xFE,0x00,0x1F,0xFE,0x00,0x1F,0xFE,0x00,0x1F,0xFE,0x00,0x1F,0xFE,0x00,0x1F,0xFE,0x00,0x1F,0xFE,0x00,0x1F,0xFE,0x00,0x1F,0xFE,0x00,0x1F,0xFE,0x00,0x1F,0xFE,0x00,0x1F,0xFE,0x00,0x1F,0xFE,0x00,0x1F,0xFE,0x00,0x1F,0xFE,0x00,0x1F,0xFE,0x00,0x1F,0xFE,0x00,0x1F,0xFE,0x00,0x1F,0xFE,0x00,0x1F,0xFE,0x00,0x1F,0xFE,0x00,0x1F,0xFE,0x00,0x1F,0xFE,0x00,0x1F,0xFE,0x00,0x1F,0xFE,0x00,0x1F,0xFE,0x00,0x1F,0xFE, // '1'
	0x00,0x07,0xFE,0x00,0x00,0x01,0xFF,0xFF,0x00,0x00,0x3F,0xFF,0xFE,0x00,0x03,0xFF,0xFF,0xF8,0x00,0x3F,0xFF,0xFF,0xE0,0x07,0xFF,0xFF,0xFF,0x80,0x3F,0xFF,0xFF,0xFE,0x03,0xFF,0xFF,0xFF,0xF0,0x3F,0xFF,0xFF,0xFF,0xC1,0xFF,0xF0,0x7F,0xFE,0x1F,0xFF,0x00,0xFF,0xF0,0xFF,0xF0,0x07,0xFF,0xC7,0xFF,0x80,0x1F,0xFE,0x3F,0xF8,0x00,0xFF,0xF3,0xFF,0xC0,0x07,0xFF,0x9F,0xFE,0x00,0x3F,0xFC,0xFF,0xF0,0x01,0xFF,0xE0,0x00,0x00,0x0F,0xFE,0x00,0x00,0x00,0xFF,0xF0,0x00,0x00,0x07,0xFF,0x80,0x00,0x00,0x7F,0xF8,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x03,0xFF,0xE0,0x00,0x00,0x3F,0xFE,0x00,0x00,0x01,0xFF,0xE0,0x00,0x00,0x1F,0xFF,0x00,0x00,0x01,0xFF,0xF0,0x00,0x00,0x1F,0xFF,0x00,0x00,0x01,0xFF,0xF0,0x00,0x00,0x1F,0xFF,0x00,0x00,0x01,0xFF,0xF0,0x00,0x00,0x1F,0xFF,0x00,0x00,0x01,0xFF,0xF0,0x00,0x00,0x1F,0xFF,0x00,0x00,0x01,0xFF,0xF0,0x00,0x00,0x1F,0xFF,0x00,0x00,0x01,0xFF,0xF8,0x00,0x00,0x1F,0xFF,0x80,0x00,0x01,0xFF,0xF8,0x00,0x00,0x1F,0xFF,0x80,0x00,0x01,0xFF,0xFF,0xFF,0xFF,0x9F,0xFF,0xFF,0xFF,0xFC,0xFF,0xFF,0xFF,0xFF,0xE7,0xFF,0xFF,0xFF,0xFF,0x3F,0xFF,0xFF,0xFF,0xF9,0xFF,0xFF,0xFF,0xFF,0xCF,0xFF,0xFF,0xFF,0xFE,0x7F,0xFF,0xFF,0xFF,0xF3,0xFF,0xFF,0xFF,0xFF,0x80, // '2'
	0x00,0x07,0xFE,0x00,0x00,0x03,0xFF,0xFE,0x00,0x00,0x7F,0xFF,0xFE,0x00,0x07,0xFF,0xFF,0xF8,0x00,0x7F,0xFF,0xFF,0xE0,0x07,0xFF,0xFF,0xFF,0x80,0x7F,0xFF,0xFF,0xFE,0x07,0xFF,0xFF,0xFF,0xF8,0x3F,0xFF,0xFF,0xFF,0xC3,0xFF,0xF0,0x3F,0xFF,0x1F,0xFF,0x00,0xFF,0xF8,0xFF,0xF0,0x03,0xFF,0xC7,0xFF,0x00,0x1F,0xFE,0x00,0x00,0x00,0x7F,0xF0,0x00,0x00,0x03,0xFF,0x80,0x00,0x00,0x1F,0xFC,0x00,0x00,0x01,0xFF,0xE0,0x00,0x00,0x0F,0xFF,0x00,0x00,0x00,0xFF,0xF0,0x00,0x00,0x1F,0xFF,0x00,0x00,0xFF,0xFF,0xF8,0x00,0x07,0xFF,0xFF,0x80,0x00,0x3F,0xFF,0xF8,0x00,0x01,0xFF,0xFF,0x00,0x00,0x0F,0xFF,0xF0,0x00,0x00,0x7F,0xFF,0xE0,0x00,0x03,0xFF,0xFF,0x80,0x00,0x1F,0xFF,0xFF,0x00,0x00,0xFF,0xFF,0xFC,0x00,0x00,0x03,0xFF,0xE0,0x00,0x00,0x0F,0xFF,0x80,0x00,0x00,0x3F,0xFC,0x00,0x00,0x00,0xFF,0xF0,0x00,0x00,0x07,0xFF,0x80,0x00,0x00,0x3F,0xFC,0x00,0x00,0x01,0xFF,0xEF,0xFE,0x00,0x0F,0xFF,0x7F,0xF8,0x00,0x7F,0xFB,0xFF,0xC0,0x03,0xFF,0xDF,0xFF,0x00,0x3F,0xFE,0xFF,0xF8,0x03,0xFF,0xE3,0xFF,0xF0,0x3F,0xFF,0x1F,0xFF,0xFF,0xFF,0xF8,0x7F,0xFF,0xFF,0xFF,0x83,0xFF,0xFF,0xFF,0xF8,0x0F,0xFF,0xFF,0xFF,0x80,0x3F,0xFF,0xFF,0xF8,0x00,0xFF,0xFF,0xFF,0x80,0x01,0xFF,0xFF,0xF8,0x00,0x03,0xFF,0xFE,0x00,0x00,0x01,0xFF,0x80,0x00, // '3'
	0x00,0x00,0x0F,0xFF,0x00,0x00,0x00,0x7F,0xFC,0x00,0x00,0x01,0xFF,0xF0,0x00,0x00,0x0F,0xFF,0xC0,0x00,0x00,0x7F,0xFF,0x00,0x00,0x01,0xFF,0xFC,0x00,0x00,0x0F,0xFF,0xF0,0x00,0x00,0x3F,0xFF,0xC0,0x00,0x01,0xFF,0xFF,0x00,0x00,0x0F,0xFF,0xFC,0x00,0x00,0x3F,0xFF,0xF0,0x00,0x01,0xFF,0xFF,0xC0,0x00,0x0F,0xFF,0xFF,0x00,0x00,0x3F,0xFF,0xFC,0x00,0x01,0xFF,0xFF,0xF0,0x00,0x07,0xFF,0xFF,0xC0,0x00,0x3F,0xEF,0xFF,0x00,0x01,0xFF,0xBF,0xFC,0x00,0x07,0xFC,0xFF,0xF0,0x00,0x3F,0xF3,0xFF,0xC0,0x01,0xFF,0x8F,0xFF,0x00,0x07,0xFC,0x3F,0xFC,0x00,0x3F,0xF0,0xFF,0xF0,0x00,0xFF,0x83,0xFF,0xC0,0x07,0xFE,0x0F,0xFF,0x00,0x3F,0xF0,0x3F,0xFC,0x00,0xFF,0x80,0xFF,0xF0,0x07,0xFE,0x03,0xFF,0xC0,0x3F,0xF0,0x0F,0xFF,0x00,0xFF,0xC0,0x3F,0xFC,0x07,0xFF,0xFF,0xFF,0xFF,0xBF,0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,0xFF,0xFF,0xFB,0xFF,0xFF,0xFF,0xFF,0xE7,0xFF,0xFF,0xFF,0xFF,0x9F,0xFF,0xFF,0xFF,0xFE,0x7F,0xFF,0xFF,0xFF,0xF9,0xFF,0xFF,0xFF,0xFF,0xE7,0xFF,0xFF,0xFF,0xFF,0x80,0x00,0x03,0xFF,0xC0,0x00,0x00,0x0F,0xFF,0x00,0x00,0x00,0x3F,0xFC,0x00,0x00,0x00,0xFF,0xF0,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0x0F,0xFF,0x00,0x00,0x00,0x3F,0xFC,0x00,0x00,0x00,0xFF,0xF0,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0x0F,0xFF,0x00,0x00,0x00,0x3F,0xFC,0x00, // '4'
	0x0F,0xFF,0xFF,0xFF,0x80,0xFF,0xFF,0xFF,0xF8,0x0F,0xFF,0xFF,0xFF,0x80,0xFF,0xFF,0xFF,0xF8,0x1F,0xFF,0xFF,0xFF,0x81,0xFF,0xFF,0xFF,0xF8,0x1F,0xFF,0xFF,0xFF,0x81,0xFF,0xFF,0xFF,0xF8,0x1F,0xFF,0xFF,0xFF,0x81,0xFF,0x80,0x00,0x00,0x1F,0xF8,0x00,0x00,0x01,0xFF,0x80,0x00,0x00,0x3F,0xF8,0x00,0x00,0x03,0xFF,0x80,0x00,0x00,0x3F,0xF8,0x00,0x00,0x03,0xFF,0x80,0x00,0x00,0x3F,0xF8,0x00,0x00,0x03,0xFF,0x0F,0xF0,0x00,0x3F,0xF7,0xFF,0xE0,0x03,0xFF,0xFF,0xFF,0x80,0x3F,0xFF,0xFF,0xFC,0x07,0xFF,0xFF,0xFF,0xE0,0x7F,0xFF,0xFF,0xFF,0x07,0xFF,0xFF,0xFF,0xF0,0x7F,0xFF,0xFF,0xFF,0x87,0xFF,0x81,0xFF,0xF8,0x07,0xE0,0x07,0xFF,0xC0,0x04,0x00,0x3F,0xFC,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0x1F,0xFC,0x00,0x00,0x01,0xFF,0xE0,0x00,0x00,0x1F,0xFE,0x00,0x00,0x01,0xFF,0xE0,0x00,0x00,0x1F,0xFE,0x00,0x00,0x01,0xFF,0xE0,0x00,0x00,0x1F,0xFE,0xFF,0xF0,0x01,0xFF,0xEF,0xFF,0x00,0x1F,0xFC,0xFF,0xF0,0x03,0xFF,0xCF,0xFF,0x80,0x3F,0xFC,0x7F,0xF8,0x07,0xFF,0xC7,0xFF,0xE0,0xFF,0xF8,0x3F,0xFF,0xFF,0xFF,0x83,0xFF,0xFF,0xFF,0xF0,0x1F,0xFF,0xFF,0xFF,0x00,0xFF,0xFF,0xFF,0xE0,0x07,0xFF,0xFF,0xFC,0x00,0x3F,0xFF,0xFF,0x80,0x00,0xFF,0xFF,0xE0,0x00,0x03,0xFF,0xF8,0x00,0x00,0x07,0xFC,0x00,0x00, // '5'
	0x00,0x00,0x07,0xF0,0x00,0x00,0x03,0xFF,0x80,0x00,0x00,0x7F,0xFC,0x00,0x00,0x0F,0xFF,0xE0,0x00,0x01,0xFF,0xFF,0x00,0x00,0x1F,0xFF,0xF8,0x00,0x01,0xFF,0xFF,0xC0,0x00,0x3F,0xFF,0xFE,0x00,0x01,0xFF,0xFF,0xF0,0x00,0x1F,0xFF,0xF0,0x00,0x01,0xFF,0xFC,0x00,0x00,0x1F,0xFF,0x80,0x00,0x00,0xFF,0xF8,0x00,0x00,0x0F,0xFF,0x00,0x00,0x00,0x7F,0xF8,0x00,0x00,0x07,0xFF,0x80,0x00,0x00,0x3F,0xF8,0x00,0x00,0x03,0xFF,0xC1,0xFE,0x00,0x1F,0xFC,0x7F,0xFC,0x00,0xFF,0xEF,0xFF,0xF8,0x07,0xFF,0xFF,0xFF,0xE0,0x7F,0xFF,0xFF,0xFF,0x83,0xFF,0xFF,0xFF,0xFE,0x1F,0xFF,0xFF,0xFF,0xF0,0xFF,0xFF,0xFF,0xFF,0xC7,0xFF,0xFF,0xFF,0xFE,0x3F,0xFF,0x83,0xFF,0xF9,0xFF,0xF0,0x07,0xFF,0xCF,0xFF,0x00,0x1F,0xFE,0x7F,0xF8,0x00,0xFF,0xF3,0xFF,0xC0,0x03,0xFF,0xDF,0xFE,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0xFF,0xF7,0xFF,0x80,0x07,0xFF,0xBF,0xFC,0x00,0x3F,0xFD,0xFF,0xE0,0x01,0xFF,0xE7,0xFF,0x00,0x0F,0xFF,0x3F,0xF8,0x00,0x7F,0xF1,0xFF,0xE0,0x07,0xFF,0x8F,0xFF,0x00,0x3F,0xFC,0x3F,0xFC,0x03,0xFF,0xE1,0xFF,0xF0,0x7F,0xFE,0x07,0xFF,0xFF,0xFF,0xF0,0x3F,0xFF,0xFF,0xFF,0x00,0xFF,0xFF,0xFF,0xF0,0x03,0xFF,0xFF,0xFF,0x80,0x0F,0xFF,0xFF,0xF8,0x00,0x3F,0xFF,0xFF,0x00,0x00,0x7F,0xFF,0xF0,0x00,0x00,0xFF,0xFE,0x00,0x00,0x00,0xFF,0x80,0x00, // '6'
	0xFF,0xFF,0xFF,0xFF,0xF7,0xFF,0xFF,0xFF,0xFF,0xBF,0xFF,0xFF,0xFF,0xFD,0xFF,0xFF,0xFF,0xFF,0xEF,0xFF,0xFF,0xFF,0xFF,0x7F,0xFF,0xFF,0xFF,0xFB,0xFF,0xFF,0xFF,0xFF,0xDF,0xFF,0xFF,0xFF,0xFC,0xFF,0xFF,0xFF,0xFF,0xE0,0x00,0x00,0x0F,0xFE,0x00,0x00,0x00,0x7F,0xF0,0x00,0x00,0x07,0xFF,0x00,0x00,0x00,0x3F,0xF8,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0x1F,0xFC,0x00,0x00,0x00,0xFF,0xE0,0x00,0x00,0x0F,0xFE,0x00,0x00,0x00,0x7F,0xF0,0x00,0x00,0x07,0xFF,0x00,0x00,0x00,0x3F,0xF8,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0x1F,0xFC,0x00,0x00,0x01,0xFF,0xE0,0x00,0x00,0x0F,0xFE,0x00,0x00,0x00,0xFF,0xF0,0x00,0x00,0x07,0xFF,0x00,0x00,0x00,0x3F,0xF8,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0x1F,0xFC,0x00,0x00,0x01,0xFF,0xE0,0x00,0x00,0x0F,0xFE,0x00,0x00,0x00,0xFF,0xF0,0x00,0x00,0x07,0xFF,0x00,0x00,0x00,0x7F,0xF8,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x01,0xFF,0xE0,0x00,0x00,0x0F,0xFE,0x00,0x00,0x00,0xFF,0xF0,0x00,0x00,0x07,0xFF,0x80,0x00,0x00,0x7F,0xF8,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x01,0xFF,0xE0,0x00,0x00,0x1F,0xFE,0x00,0x00,0x00,0xFF,0xF0,0x00,0x00,0x0F,0xFF,0x80,0x00,0x00,0x7F,0xF8,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x00, // '7'
	0x00,0x0F,0xFC,0x00,0x00,0x07,0xFF,0xFC,0x00,0x01,0xFF,0xFF,0xF0,0x00,0x3F,0xFF,0xFF,0x80,0x07,0xFF,0xFF,0xFC,0x00,0xFF,0xFF,0xFF,0xE0,0x1F,0xFF,0xFF,0xFF,0x01,0xFF,0xFF,0xFF,0xF0,0x3F,0xFF,0xFF,0xFF,0x83,0xFF,0xE0,0xFF,0xF8,0x7F,0xFC,0x07,0xFF,0xC7,0xFF,0x80,0x3F,0xFC,0x7F,0xF8,0x03,0xFF,0xC7,0xFF,0x80,0x3F,0xFC,0x7F,0xF8,0x03,0xFF,0xC7,0xFF,0x80,0x3F,0xFC,0x7F,0xF8,0x03,0xFF,0xC3,0xFF,0x80,0x3F,0xF8,0x3F,0xFC,0x07,0xFF,0x81,0xFF,0xE0,0xFF,0xF0,0x1F,0xFF,0xFF,0xFF,0x00,0xFF,0xFF,0xFF,0xE0,0x07,0xFF,0xFF,0xFC,0x00,0x1F,0xFF,0xFF,0x80,0x00,0xFF,0xFF,0xE0,0x00,0x3F,0xFF,0xFF,0x80,0x07,0xFF,0xFF,0xFC,0x00,0xFF,0xFF,0xFF,0xE0,0x1F,0xFF,0xFF,0xFF,0x03,0xFF,0xE0,0xFF,0xF8,0x7F,0xF8,0x07,0xFF,0x87,0xFF,0x80,0x3F,0xFC,0x7F,0xF0,0x01,0xFF,0xCF,0xFF,0x00,0x1F,0xFE,0xFF,0xF0,0x01,0xFF,0xEF,0xFF,0x00,0x1F,0xFE,0xFF,0xF0,0x01,0xFF,0xEF,0xFF,0x00,0x1F,0xFE,0xFF,0xF0,0x01,0xFF,0xEF,0xFF,0x80,0x3F,0xFE,0x7F,0xFC,0x07,0xFF,0xC7,0xFF,0xE0,0xFF,0xFC,0x7F,0xFF,0xFF,0xFF,0xC3,0xFF,0xFF,0xFF,0xF8,0x3F,0xFF,0xFF,0xFF,0x81,0xFF,0xFF,0xFF,0xF0,0x0F,0xFF,0xFF,0xFE,0x00,0x7F,0xFF,0xFF,0xC0,0x01,0xFF,0xFF,0xF0,0x00,0x07,0xFF,0xFC,0x00,0x00,0x0F,0xFE,0x00,0x00, // '8'
	0x00,0x07,0xF8,0x00,0x00,0x07,0xFF,0xE0,0x00,0x03,0xFF,0xFF,0x00,0x01,0xFF,0xFF,0xF0,0x00,0x7F,0xFF,0xFF,0x80,0x1F,0xFF,0xFF,0xF8,0x07,0xFF,0xFF,0xFF,0x00,0xFF,0xFF,0xFF,0xF0,0x3F,0xFF,0xFF,0xFF,0x07,0xFF,0xC3,0xFF,0xE1,0xFF,0xF0,0x1F,0xFE,0x3F,0xFC,0x01,0xFF,0xCF,0xFF,0x00,0x3F,0xF9,0xFF,0xE0,0x07,0xFF,0xBF,0xFC,0x00,0x7F,0xF7,0xFF,0x00,0x0F,0xFE,0xFF,0xE0,0x01,0xFF,0xDF,0xFC,0x00,0x3F,0xFB,0xFF,0x80,0x07,0xFF,0x7F,0xF0,0x00,0xFF,0xEF,0xFF,0x00,0x1F,0xFD,0xFF,0xE0,0x03,0xFF,0xBF,0xFE,0x00,0x7F,0xF7,0xFF,0xC0,0x1F,0xFE,0x7F,0xFE,0x0F,0xFF,0xCF,0xFF,0xFF,0xFF,0xF8,0xFF,0xFF,0xFF,0xFF,0x1F,0xFF,0xFF,0xFF,0xE1,0xFF,0xFF,0xFF,0xFC,0x1F,0xFF,0xFF,0xFF,0x81,0xFF,0xFF,0xFF,0xF0,0x1F,0xFF,0xEF,0xFE,0x01,0xFF,0xF3,0xFF,0x80,0x07,0xF8,0x7F,0xF0,0x00,0x00,0x0F,0xFE,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0x7F,0xF0,0x00,0x00,0x1F,0xFE,0x00,0x00,0x07,0xFF,0x80,0x00,0x01,0xFF,0xF0,0x00,0x00,0xFF,0xFC,0x00,0x00,0xFF,0xFF,0x00,0x07,0xFF,0xFF,0xE0,0x00,0xFF,0xFF,0xF8,0x00,0x1F,0xFF,0xFE,0x00,0x03,0xFF,0xFF,0x00,0x00,0x7F,0xFF,0xC0,0x00,0x0F,0xFF,0xE0,0x00,0x01,0xFF,0xF8,0x00,0x00,0x3F,0xF8,0x00,0x00,0x07,0xF0,0x00,0x00,0x00, // '9'
	0x0F,0x80,0xFF,0x87,0xFF,0x3F,0xFC,0xFF,0xFB,0xFF,0xEF,0xFF,0xBF,0xFC,0x7F,0xF0,0xFF,0x80,0xF8,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0xE0,0x3F,0xE1,0xFF,0xCF,0xFF,0x3F,0xFE,0xFF,0xFB,0xFF,0xEF,0xFF,0x1F,0xFC,0x3F,0xE0,0x3E,0x00, // ':'
	0x07,0xC0,0x3F,0xE0,0xFF,0xE3,0xFF,0xC7,0xFF,0xCF,0xFF,0x9F,0xFF,0x3F,0xFC,0x3F,0xF8,0x3F,0xE0,0x1F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0xFE,0x07,0xFC,0x0F,0xF8,0x1F,0xF0,0x3F,0xE0,0x7F,0xC0,0xFF,0x81,0xFF,0x03,0xFE,0x07,0xFC,0x0F,0xF8,0x1F,0xF0,0x3F,0xE0,0xFF,0x81,0xFF,0x07,0xFC,0x0F,0xF8,0x3F,0xE0,0x7F,0xC0,0xFF,0x00,0x7C,0x00,0x30,0x00, // ';'
	0x00,0x00,0x00,0x04,0x00,0x00,0x00,0x78,0x00,0x00,0x03,0xF0,0x00,0x00,0x3F,0xE0,0x00,0x01,0xFF,0xC0,0x00,0x1F,0xFF,0x80,0x00,0xFF,0xFF,0x00,0x07,0xFF,0xFE,0x00,0x7F,0xFF,0xFC,0x03,0xFF,0xFF,0xF8,0x3F,0xFF,0xFF,0xF1,0xFF,0xFF,0xFF,0x0F,0xFF,0xFF,0xF0,0x1F,0xFF,0xFF,0x00,0x3F,0xFF,0xF0,0x00,0x7F,0xFF,0x80,0x00,0xFF,0xF8,0x00,0x01,0xFF,0xE0,0x00,0x03,0xFF,0xF8,0x00,0x07,0xFF,0xFE,0x00,0x0F,0xFF,0xFF,0x80,0x1F,0xFF,0xFF,0xE0,0x0F,0xFF,0xFF,0xF8,0x07,0xFF,0xFF,0xFE,0x01,0xFF,0xFF,0xFC,0x00,0xFF,0xFF,0xF8,0x00,0x3F,0xFF,0xF0,0x00,0x1F,0xFF,0xE0,0x00,0x0F,0xFF,0xC0,0x00,0x03,0xFF,0x80,0x00,0x01,0xFF,0x00,0x00,0x00,0x7E,0x00,0x00,0x00,0x3C,0x00,0x00,0x00,0x08, // '<'
	0xFF,0xFF,0xFF,0xFF,0x7F,0xFF,0xFF,0xFF,0xBF,0xFF,0xFF,0xFF,0xDF,0xFF,0xFF,0xFF,0xEF,0xFF,0xFF,0xFF,0xF7,0xFF,0xFF,0xFF,0xFB,0xFF,0xFF,0xFF,0xFD,0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,0xFF,0xFF,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,0xFF,0xFF,0x7F,0xFF,0xFF,0xFF,0xBF,0xFF,0xFF,0xFF,0xDF,0xFF,0xFF,0xFF,0xEF,0xFF,0xFF,0xFF,0xF7,0xFF,0xFF,0xFF,0xFB,0xFF,0xFF,0xFF,0xFD,0xFF,0xFF,0xFF,0xFE, // '='
	0x80,0x00,0x00,0x01,0xC0,0x00,0x00,0x03,0xF0,0x00,0x00,0x07,0xF8,0x00,0x00,0x0F,0xFE,0x00,0x00,0x1F,0xFF,0x00,0x00,0x3F,0xFF,0xC0,0x00,0x7F,0xFF,0xE0,0x00,0xFF,0xFF,0xF0,0x01,0xFF,0xFF,0xFC,0x03,0xFF,0xFF,0xFE,0x00,0xFF,0xFF,0xFF,0x80,0x7F,0xFF,0xFF,0xC0,0x1F,0xFF,0xFF,0x80,0x07,0xFF,0xFF,0x00,0x01,0xFF,0xFE,0x00,0x00,0x7F,0xFC,0x00,0x00,0xFF,0xF8,0x00,0x0F,0xFF,0xF0,0x00,0xFF,0xFF,0xE0,0x0F,0xFF,0xFF,0xC0,0xFF,0xFF,0xFF,0x87,0xFF,0xFF,0xFC,0x7F,0xFF,0xFF,0xC0,0xFF,0xFF,0xFE,0x01,0xFF,0xFF,0xE0,0x03,0xFF,0xFF,0x00,0x07,0xFF,0xF8,0x00,0x0F,0xFF,0x80,0x00,0x1F,0xFC,0x00,0x00,0x3F,0xC0,0x00,0x00,0x7E,0x00,0x00,0x00,0xE0,0x00,0x00,0x01,0x00,0x00,0x00,0x00, // '>'
	0x00,0x1F,0xFC,0x00,0x00,0x7F,0xFF,0xC0,0x00,0xFF,0xFF,0xF8,0x00,0xFF,0xFF,0xFE,0x00,0xFF,0xFF,0xFF,0x80,0xFF,0xFF,0xFF,0xE0,0xFF,0xFF,0xFF,0xF8,0x7F,0xFF,0xFF,0xFC,0x7F,0xFF,0xFF,0xFF,0x3F,0xFE,0x0F,0xFF,0x9F,0xFE,0x03,0xFF,0xDF,0xFE,0x01,0xFF,0xEF,0xFF,0x00,0x7F,0xF7,0xFF,0x80,0x3F,0xF8,0x00,0x00,0x1F,0xFC,0x00,0x00,0x0F,0xFE,0x00,0x00,0x0F,0xFF,0x00,0x00,0x07,0xFF,0x80,0x00,0x07,0xFF,0x80,0x00,0x07,0xFF,0xC0,0x00,0x07,0xFF,0xC0,0x00,0x07,0xFF,0xC0,0x00,0x07,0xFF,0xC0,0x00,0x07,0xFF,0xC0,0x00,0x07,0xFF,0xC0,0x00,0x07,0xFF,0xC0,0x00,0x03,0xFF,0xC0,0x00,0x01,0xFF,0xC0,0x00,0x01,0xFF,0xC0,0x00,0x00,0xFF,0xE0,0x00,0x00,0x7F,0xE0,0x00,0x00,0x3F,0xF0,0x00,0x00,0x1F,0xF8,0x00,0x00,0x0F,0xFC,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0F,0xC0,0x00,0x00,0x1F,0xF0,0x00,0x00,0x1F,0xFC,0x00,0x00,0x0F,0xFF,0x00,0x00,0x07,0xFF,0x80,0x00,0x03,0xFF,0xC0,0x00,0x01,0xFF,0xE0,0x00,0x00,0xFF,0xF0,0x00,0x00,0x7F,0xF0,0x00,0x00,0x1F,0xF0,0x00,0x00,0x03,0xF0,0x00,0x00, // '?'
	0x00,0x00,0x00,0x7F,0xF8,0x00,0x00,0x00,0x00,0x00,0xFF,0xFF,0xF0,0x00,0x00,0x00,0x00,0x7F,0xFF,0xFF,0xC0,0x00,0x00,0x00,0x7F,0xFF,0xFF,0xFE,0x00,0x00,0x00,0x1F,0xFF,0xFF,0xFF,0xE0,0x00,0x00,0x0F,0xFF,0xFF,0xFF,0xFF,0x00,0x00,0x03,0xFF,0xF0,0x03,0xFF,0xF0,0x00,0x00,0xFF,0xF0,0x00,0x07,0xFF,0x00,0x00,0x3F,0xF8,0x00,0x00,0x3F,0xF0,0x00,0x0F,0xFC,0x00,0x00,0x03,0xFF,0x00,0x03,0xFF,0x00,0x00,0x00,0x1F,0xE0,0x00,0xFF,0x80,0x00,0x00,0x01,0xFE,0x00,0x3F,0xE0,0x00,0x00,0x00,0x1F,0xE0,0x07,0xF8,0x00,0x00,0x00,0x03,0xFC,0x01,0xFF,0x00,0x03,0xFC,0x00,0x3F,0x80,0x3F,0xC0,0x03,0xFF,0xF0,0x03,0xF8,0x0F,0xF0,0x00,0xFF,0xFF,0x80,0x7F,0x01,0xFE,0x00,0x3F,0xFF,0xF8,0x07,0xF0,0x7F,0x80,0x1F,0xFF,0xFF,0x80,0xFE,0x0F,0xF0,0x03,0xFF,0xFF,0xE0,0x1F,0xC3,0xFC,0x00,0xFF,0xFF,0xFC,0x03,0xF8,0x7F,0x80,0x3F,0xF8,0x3F,0x80,0x3F,0x8F,0xF0,0x07,0xFC,0x0F,0xF0,0x07,0xF1,0xFC,0x01,0xFF,0x01,0xFE,0x00,0xFE,0x7F,0x80,0x3F,0xC0,0x3F,0xC0,0x1F,0xCF,0xF0,0x0F,0xF8,0x07,0xF8,0x03,0xF9,0xFE,0x01,0xFE,0x00,0xFF,0x00,0x7F,0x3F,0x80,0x7F,0xC0,0x1F,0xE0,0x0F,0xE7,0xF0,0x0F,0xF8,0x03,0xFC,0x01,0xFC,0xFE,0x01,0xFE,0x00,0x7F,0x80,0x3F,0xBF,0xC0,0x3F,0xC0,0x0F,0xE0,0x07,0xF7,0xF8,0x07,0xF8,0x01,0xFC,0x00,0xFE,0xFF,0x01,0xFF,0x00,0x3F,0x80,0x1F,0xDF,0xE0,0x3F,0xE0,0x0F,0xF0,0x03,0xFB,0xFC,0x07,0xFC,0x01,0xFE,0x00,0x7E,0x7F,0x80,0xFF,0x80,0x3F,0xC0,0x1F,0xCF,0xF0,0x1F,0xF0,0x07,0xF8,0x03,0xF9,0xFE,0x03,0xFE,0x00,0xFF,0x00,0x7F,0x1F,0xC0,0x7F,0xC0,0x3F,0xE0,0x1F,0xC3,0xF8,0x0F,0xF8,0x07,0xFC,0x03,0xF8,0x7F,0x00,0xFF,0x81,0xFF,0x80,0xFF,0x0F,0xF0,0x1F,0xF8,0x7F,0xF0,0x3F,0xC1,0xFE,0x03,0xFF,0xFF,0xFF,0x9F,0xF0,0x3F,0xC0,0x3F,0xFF,0xFF,0xFF,0xFE,0x03,0xF8,0x07,0xFF,0xFF,0xFF,0xFF,0x80,0x7F,0x80,0x7F,0xFF,0x3F,0xFF,0xE0,0x0F,0xF0,0x07,0xFF,0xC3,0xFF,0xF8,0x00,0xFF,0x00,0x7F,0xF0,0x3F,0xFC,0x00,0x1F,0xF0,0x03,0xF8,0x01,0xFE,0x00,0x03,0xFE,0x00,0x00,0x00,0x00,0x00,0x00,0x3F,0xE0,0x00,0x00,0x00,0x00,0x00,0x07,0xFE,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0xF0,0x00,0x00,0x00,0x00,0x00,0x07,0xFF,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0xF8,0x00,0x00,0xC0,0x00,0x00,0x0F,0xFF,0xF0,0x01,0xF8,0x00,0x00,0x00,0x7F,0xFF,0xFF,0xFF,0x80,0x00,0x00,0x07,0xFF,0xFF,0xFF,0xF0,0x00,0x00,0x00,0x7F,0xFF,0xFF,0xFE,0x00,0x00,0x00,0x03,0xFF,0xFF,0xFF,0xC0,0x00,0x00,0x00,0x1F,0xFF,0xFF,0xF0,0x00,0x00,0x00,0x00,0xFF,0xFF,0xF8,0x00,0x00,0x00,0x00,0x00,0xFF,0xF0,0x00,0x00,0x00, // '@'
	0x00,0x00,0x3F,0xFC,0x00,0x00,0x00,0x00,0x1F,0xFE,0x00,0x00,0x00,0x00,0x1F,0xFF,0x00,0x00,0x00,0x00,0x0F,0xFF,0xC0,0x00,0x00,0x00,0x07,0xFF,0xE0,0x00,0x00,0x00,0x07,0xFF,0xF0,0x00,0x00,0x00,0x03,0xFF,0xFC,0x00,0x00,0x00,0x03,0xFF,0xFE,0x00,0x00,0x00,0x01,0xFF,0xFF,0x80,0x00,0x00,0x00,0xFF,0xFF,0xC0,0x00,0x00,0x00,0xFF,0xFF,0xE0,0x00,0x00,0x00,0x7F,0xFF,0xF8,0x00,0x00,0x00,0x3F,0xFF,0xFC,0x00,0x00,0x00,0x3F,0xFF,0xFE,0x00,0x00,0x00,0x1F,0xFF,0xFF,0x80,0x00,0x00,0x1F,0xFD,0xFF,0xC0,0x00,0x00,0x0F,0xFE,0x7F,0xE0,0x00,0x00,0x07,0xFF,0x3F,0xF8,0x00,0x00,0x07,0xFF,0x1F,0xFC,0x00,0x00,0x03,0xFF,0x87,0xFF,0x00,0x00,0x01,0xFF,0xC3,0xFF,0x80,0x00,0x01,0xFF,0xC1,0xFF,0xC0,0x00,0x00,0xFF,0xE0,0xFF,0xF0,0x00,0x00,0x7F,0xF0,0x3F,0xF8,0x00,0x00,0x7F,0xF8,0x1F,0xFC,0x00,0x00,0x3F,0xF8,0x0F,0xFF,0x00,0x00,0x3F,0xFC,0x03,0xFF,0x80,0x00,0x1F,0xFE,0x01,0xFF,0xE0,0x00,0x0F,0xFE,0x00,0xFF,0xF0,0x00,0x0F,0xFF,0x00,0x3F,0xF8,0x00,0x07,0xFF,0x80,0x1F,0xFE,0x00,0x03,0xFF,0xFF,0xFF,0xFF,0x00,0x03,0xFF,0xFF,0xFF,0xFF,0x80,0x01,0xFF,0xFF,0xFF,0xFF,0xE0,0x00,0xFF,0xFF,0xFF,0xFF,0xF0,0x00,0xFF,0xFF,0xFF,0xFF,0xF8,0x00,0x7F,0xFF,0xFF,0xFF,0xFE,0x00,0x7F,0xFF,0xFF,0xFF,0xFF,0x00,0x3F,0xFF,0xFF,0xFF,0xFF,0xC0,0x1F,0xFF,0xFF,0xFF,0xFF,0xE0,0x1F,0xFE,0x00,0x00,0xFF,0xF0,0x0F,0xFF,0x00,0x00,0x7F,0xFC,0x07,0xFF,0x80,0x00,0x1F,0xFE,0x07,0xFF,0xC0,0x00,0x0F,0xFF,0x03,0xFF,0xC0,0x00,0x07,0xFF,0xC1,0xFF,0xE0,0x00,0x01,0xFF,0xE1,0xFF,0xF0,0x00,0x00,0xFF,0xF8,0xFF,0xF0,0x00,0x00,0x7F,0xFC,0xFF,0xF8,0x00,0x00,0x1F,0xFE,0x7F,0xFC,0x00,0x00,0x0F,0xFF,0x80, // 'A'
	0xFF,0xFF,0xFF,0x00,0x01,0xFF,0xFF,0xFF,0xE0,0x03,0xFF,0xFF,0xFF,0xF0,0x07,0xFF,0xFF,0xFF,0xF8,0x0F,0xFF,0xFF,0xFF,0xF8,0x1F,0xFF,0xFF,0xFF,0xF8,0x3F,0xFF,0xFF,0xFF,0xF8,0x7F,0xFF,0xFF,0xFF,0xF8,0xFF,0xFF,0xFF,0xFF,0xF1,0xFF,0xE0,0x0F,0xFF,0xF3,0xFF,0xC0,0x07,0xFF,0xE7,0xFF,0x80,0x07,0xFF,0xCF,0xFF,0x00,0x07,0xFF,0x9F,0xFE,0x00,0x0F,0xFF,0x3F,0xFC,0x00,0x1F,0xFE,0x7F,0xF8,0x00,0x3F,0xFC,0xFF,0xF0,0x00,0x7F,0xF9,0xFF,0xE0,0x00,0xFF,0xF3,0xFF,0xC0,0x03,0xFF,0xC7,0xFF,0x80,0x07,0xFF,0x8F,0xFF,0x00,0x3F,0xFE,0x1F,0xFF,0xFF,0xFF,0xFC,0x3F,0xFF,0xFF,0xFF,0xF0,0x7F,0xFF,0xFF,0xFF,0xC0,0xFF,0xFF,0xFF,0xFE,0x01,0xFF,0xFF,0xFF,0xFE,0x03,0xFF,0xFF,0xFF,0xFF,0x07,0xFF,0xFF,0xFF,0xFF,0x0F,0xFF,0xFF,0xFF,0xFF,0x1F,0xFE,0x00,0x3F,0xFF,0x3F,0xFC,0x00,0x1F,0xFE,0x7F,0xF8,0x00,0x3F,0xFE,0xFF,0xF0,0x00,0x3F,0xFD,0xFF,0xE0,0x00,0x7F,0xFB,0xFF,0xC0,0x00,0xFF,0xF7,0xFF,0x80,0x01,0xFF,0xEF,0xFF,0x00,0x03,0xFF,0xDF,0xFE,0x00,0x07,0xFF,0xBF,0xFC,0x00,0x1F,0xFF,0x7F,0xF8,0x00,0x3F,0xFE,0xFF,0xF0,0x01,0xFF,0xFD,0xFF,0xFF,0xFF,0xFF,0xF3,0xFF,0xFF,0xFF,0xFF,0xE7,0xFF,0xFF,0xFF,0xFF,0x8F,0xFF,0xFF,0xFF,0xFE,0x1F,0xFF,0xFF,0xFF,0xFC,0x3F,0xFF,0xFF,0xFF,0xE0,0x7F,0xFF,0xFF,0xFF,0x80,0xFF,0xFF,0xFF,0xFC,0x01,0xFF,0xFF,0xFF,0x80,0x00, // 'B'
	0x00,0x00,0xFF,0xE0,0x00,0x00,0x00,0xFF,0xFF,0x80,0x00,0x00,0x7F,0xFF,0xFE,0x00,0x00,0x3F,0xFF,0xFF,0xE0,0x00,0x0F,0xFF,0xFF,0xFF,0x00,0x03,0xFF,0xFF,0xFF,0xF0,0x00,0xFF,0xFF,0xFF,0xFF,0x00,0x3F,0xFF,0xFF,0xFF,0xE0,0x07,0xFF,0xFF,0xFF,0xFE,0x01,0xFF,0xF8,0x0F,0xFF,0xE0,0x7F,0xFC,0x00,0x7F,0xFC,0x0F,0xFF,0x00,0x07,0xFF,0xC3,0xFF,0xE0,0x00,0x7F,0xF8,0x7F,0xF8,0x00,0x0F,0xFF,0x0F,0xFF,0x00,0x00,0xFF,0xF3,0xFF,0xC0,0x00,0x1F,0xFE,0x7F,0xF8,0x00,0x03,0xFF,0xCF,0xFF,0x00,0x00,0x7F,0xF9,0xFF,0xE0,0x00,0x00,0x00,0x3F,0xFC,0x00,0x00,0x00,0x07,0xFF,0x80,0x00,0x00,0x01,0xFF,0xF0,0x00,0x00,0x00,0x3F,0xFE,0x00,0x00,0x00,0x07,0xFF,0xC0,0x00,0x00,0x00,0xFF,0xF8,0x00,0x00,0x00,0x1F,0xFF,0x00,0x00,0x00,0x03,0xFF,0xE0,0x00,0x00,0x00,0x7F,0xFC,0x00,0x00,0x00,0x0F,0xFF,0x80,0x00,0x00,0x01,0xFF,0xF0,0x00,0x00,0x00,0x1F,0xFE,0x00,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0x00,0x7F,0xF8,0x00,0x00,0x00,0x0F,0xFF,0x00,0x00,0x00,0x01,0xFF,0xE0,0x00,0x0F,0xFF,0x3F,0xFC,0x00,0x01,0xFF,0xE3,0xFF,0xC0,0x00,0x3F,0xFC,0x7F,0xF8,0x00,0x0F,0xFF,0x0F,0xFF,0x80,0x01,0xFF,0xE0,0xFF,0xF0,0x00,0x7F,0xFC,0x1F,0xFF,0x00,0x1F,0xFF,0x01,0xFF,0xF8,0x0F,0xFF,0xE0,0x3F,0xFF,0xFF,0xFF,0xF8,0x03,0xFF,0xFF,0xFF,0xFF,0x00,0x3F,0xFF,0xFF,0xFF,0xC0,0x03,0xFF,0xFF,0xFF,0xF0,0x00,0x3F,0xFF,0xFF,0xFC,0x00,0x03,0xFF,0xFF,0xFE,0x00,0x00,0x1F,0xFF,0xFF,0x80,0x00,0x00,0xFF,0xFF,0x80,0x00,0x00,0x03,0xFF,0x80,0x00,0x00, // 'C'
	0xFF,0xFF,0xF8,0x00,0x00,0xFF,0xFF,0xFF,0x00,0x00,0xFF,0xFF,0xFF,0xE0,0x00,0xFF,0xFF,0xFF,0xF0,0x00,0xFF,0xFF,0xFF,0xFC,0x00,0xFF,0xFF,0xFF,0xFE,0x00,0xFF,0xFF,0xFF,0xFF,0x00,0xFF,0xFF,0xFF,0xFF,0x80,0xFF,0xFF,0xFF,0xFF,0xC0,0xFF,0xF0,0x1F,0xFF,0xE0,0xFF,0xF0,0x07,0xFF,0xE0,0xFF,0xF0,0x01,0xFF,0xF0,0xFF,0xF0,0x00,0xFF,0xF0,0xFF,0xF0,0x00,0xFF,0xF8,0xFF,0xF0,0x00,0x7F,0xF8,0xFF,0xF0,0x00,0x7F,0xFC,0xFF,0xF0,0x00,0x3F,0xFC,0xFF,0xF0,0x00,0x3F,0xFC,0xFF,0xF0,0x00,0x3F,0xFC,0xFF,0xF0,0x00,0x3F,0xFE,0xFF,0xF0,0x00,0x3F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x3F,0xFE,0xFF,0xF0,0x00,0x3F,0xFE,0xFF,0xF0,0x00,0x3F,0xFC,0xFF,0xF0,0x00,0x3F,0xFC,0xFF,0xF0,0x00,0x3F,0xFC,0xFF,0xF0,0x00,0x7F,0xFC,0xFF,0xF0,0x00,0x7F,0xF8,0xFF,0xF0,0x00,0xFF,0xF8,0xFF,0xF0,0x00,0xFF,0xF0,0xFF,0xF0,0x01,0xFF,0xF0,0xFF,0xF0,0x07,0xFF,0xE0,0xFF,0xF0,0x1F,0xFF,0xE0,0xFF,0xFF,0xFF,0xFF,0xC0,0xFF,0xFF,0xFF,0xFF,0x80,0xFF,0xFF,0xFF,0xFF,0x00,0xFF,0xFF,0xFF,0xFE,0x00,0xFF,0xFF,0xFF,0xFC,0x00,0xFF,0xFF,0xFF,0xF8,0x00,0xFF,0xFF,0xFF,0xE0,0x00,0xFF,0xFF,0xFF,0x80,0x00,0xFF,0xFF,0xF8,0x00,0x00, // 'D'
	0xFF,0xFF,0xFF,0xFF,0xDF,0xFF,0xFF,0xFF,0xFB,0xFF,0xFF,0xFF,0xFF,0x7F,0xFF,0xFF,0xFF,0xEF,0xFF,0xFF,0xFF,0xFD,0xFF,0xFF,0xFF,0xFF,0xBF,0xFF,0xFF,0xFF,0xF7,0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,0xFF,0xFF,0xDF,0xFE,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0x7F,0xF8,0x00,0x00,0x0F,0xFF,0x00,0x00,0x01,0xFF,0xE0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x07,0xFF,0x80,0x00,0x00,0xFF,0xF0,0x00,0x00,0x1F,0xFE,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0x7F,0xF8,0x00,0x00,0x0F,0xFF,0xFF,0xFF,0xE1,0xFF,0xFF,0xFF,0xFC,0x3F,0xFF,0xFF,0xFF,0x87,0xFF,0xFF,0xFF,0xF0,0xFF,0xFF,0xFF,0xFE,0x1F,0xFF,0xFF,0xFF,0xC3,0xFF,0xFF,0xFF,0xF8,0x7F,0xFF,0xFF,0xFF,0x0F,0xFF,0xFF,0xFF,0xE1,0xFF,0xE0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x07,0xFF,0x80,0x00,0x00,0xFF,0xF0,0x00,0x00,0x1F,0xFE,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0x7F,0xF8,0x00,0x00,0x0F,0xFF,0x00,0x00,0x01,0xFF,0xE0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x07,0xFF,0x80,0x00,0x00,0xFF,0xF0,0x00,0x00,0x1F,0xFF,0xFF,0xFF,0xFB,0xFF,0xFF,0xFF,0xFF,0x7F,0xFF,0xFF,0xFF,0xEF,0xFF,0xFF,0xFF,0xFD,0xFF,0xFF,0xFF,0xFF,0xBF,0xFF,0xFF,0xFF,0xF7,0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,0xFF,0xFF,0xDF,0xFF,0xFF,0xFF,0xF8, // 'E'
	0xFF,0xFF,0xFF,0xFF,0xBF,0xFF,0xFF,0xFF,0xEF,0xFF,0xFF,0xFF,0xFB,0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,0xFF,0xFF,0xBF,0xFF,0xFF,0xFF,0xEF,0xFF,0xFF,0xFF,0xFB,0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,0xFF,0xFF,0xBF,0xFC,0x00,0x00,0x0F,0xFF,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0xFF,0xF0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x0F,0xFF,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0xFF,0xF0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x0F,0xFF,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0xFF,0xF0,0x00,0x00,0x3F,0xFF,0xFF,0xFF,0x8F,0xFF,0xFF,0xFF,0xE3,0xFF,0xFF,0xFF,0xF8,0xFF,0xFF,0xFF,0xFE,0x3F,0xFF,0xFF,0xFF,0x8F,0xFF,0xFF,0xFF,0xE3,0xFF,0xFF,0xFF,0xF8,0xFF,0xFF,0xFF,0xFE,0x3F,0xFF,0xFF,0xFF,0x8F,0xFF,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0xFF,0xF0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x0F,0xFF,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0xFF,0xF0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x0F,0xFF,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0xFF,0xF0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x0F,0xFF,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0xFF,0xF0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x0F,0xFF,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0xFF,0xF0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x00, // 'F'
	0x00,0x00,0xFF,0xE0,0x00,0x00,0x00,0xFF,0xFF,0xC0,0x00,0x00,0x7F,0xFF,0xFE,0x00,0x00,0x3F,0xFF,0xFF,0xF0,0x00,0x0F,0xFF,0xFF,0xFF,0x00,0x03,0xFF,0xFF,0xFF,0xF0,0x00,0xFF,0xFF,0xFF,0xFF,0x00,0x3F,0xFF,0xFF,0xFF,0xE0,0x0F,0xFF,0xFF,0xFF,0xFE,0x01,0xFF,0xFC,0x0F,0xFF,0xE0,0x7F,0xFE,0x00,0x7F,0xFC,0x1F,0xFF,0x80,0x07,0xFF,0x83,0xFF,0xE0,0x00,0x7F,0xF8,0x7F,0xF8,0x00,0x0F,0xFF,0x1F,0xFF,0x00,0x00,0xFF,0xE3,0xFF,0xC0,0x00,0x1F,0xFC,0x7F,0xF8,0x00,0x03,0xFF,0xCF,0xFF,0x00,0x00,0x00,0x03,0xFF,0xE0,0x00,0x00,0x00,0x7F,0xF8,0x00,0x00,0x00,0x0F,0xFF,0x00,0x00,0x00,0x01,0xFF,0xE0,0x00,0x00,0x00,0x3F,0xFC,0x00,0x00,0x00,0x07,0xFF,0x80,0x00,0x00,0x00,0xFF,0xF0,0x07,0xFF,0xFF,0xDF,0xFE,0x00,0xFF,0xFF,0xFB,0xFF,0xC0,0x1F,0xFF,0xFF,0x7F,0xF8,0x03,0xFF,0xFF,0xEF,0xFF,0x00,0x7F,0xFF,0xFD,0xFF,0xE0,0x0F,0xFF,0xFF,0xBF,0xFC,0x01,0xFF,0xFF,0xF7,0xFF,0x80,0x3F,0xFF,0xFE,0xFF,0xF8,0x00,0x03,0xFF,0xCF,0xFF,0x00,0x00,0x7F,0xF9,0xFF,0xE0,0x00,0x0F,0xFF,0x3F,0xFC,0x00,0x01,0xFF,0xE7,0xFF,0xC0,0x00,0x3F,0xFC,0x7F,0xF8,0x00,0x07,0xFF,0x8F,0xFF,0x80,0x00,0xFF,0xF1,0xFF,0xF8,0x00,0x1F,0xFE,0x1F,0xFF,0x80,0x07,0xFF,0xC3,0xFF,0xFC,0x03,0xFF,0xF8,0x3F,0xFF,0xFF,0xFF,0xFF,0x03,0xFF,0xFF,0xFF,0xFF,0xE0,0x3F,0xFF,0xFF,0xFF,0xF8,0x03,0xFF,0xFF,0xFF,0xFE,0x00,0x3F,0xFF,0xFF,0xFF,0x80,0x03,0xFF,0xFF,0xFF,0xC0,0x00,0x1F,0xFF,0xFF,0xE0,0x00,0x00,0xFF,0xFF,0xE0,0x00,0x00,0x01,0xFF,0xC0,0x00,0x00, // 'G'
	0xFF,0xF0,0x00,0x07,0xFF,0xBF,0xFC,0x00,0x01,0xFF,0xEF,0xFF,0x00,0x00,0x7F,0xFB,0xFF,0xC0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x07,0xFF,0xBF,0xFC,0x00,0x01,0xFF,0xEF,0xFF,0x00,0x00,0x7F,0xFB,0xFF,0xC0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x07,0xFF,0xBF,0xFC,0x00,0x01,0xFF,0xEF,0xFF,0x00,0x00,0x7F,0xFB,0xFF,0xC0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x07,0xFF,0xBF,0xFC,0x00,0x01,0xFF,0xEF,0xFF,0x00,0x00,0x7F,0xFB,0xFF,0xC0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x07,0xFF,0xBF,0xFC,0x00,0x01,0xFF,0xEF,0xFF,0x00,0x00,0x7F,0xFB,0xFF,0xC0,0x00,0x1F,0xFE,0xFF,0xFF,0xFF,0xFF,0xFF,0xBF,0xFF,0xFF,0xFF,0xFF,0xEF,0xFF,0xFF,0xFF,0xFF,0xFB,0xFF,0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,0xFF,0xFF,0xFF,0xBF,0xFF,0xFF,0xFF,0xFF,0xEF,0xFF,0xFF,0xFF,0xFF,0xFB,0xFF,0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,0xFF,0xFF,0xFF,0xBF,0xFC,0x00,0x01,0xFF,0xEF,0xFF,0x00,0x00,0x7F,0xFB,0xFF,0xC0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x07,0xFF,0xBF,0xFC,0x00,0x01,0xFF,0xEF,0xFF,0x00,0x00,0x7F,0xFB,0xFF,0xC0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x07,0xFF,0xBF,0xFC,0x00,0x01,0xFF,0xEF,0xFF,0x00,0x00,0x7F,0xFB,0xFF,0xC0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x07,0xFF,0xBF,0xFC,0x00,0x01,0xFF,0xEF,0xFF,0x00,0x00,0x7F,0xFB,0xFF,0xC0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x07,0xFF,0xBF,0xFC,0x00,0x01,0xFF,0xEF,0xFF,0x00,0x00,0x7F,0xFB,0xFF,0xC0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x07,0xFF,0xBF,0xFC,0x00,0x01,0xFF,0xE0, // 'H'
	0xFF,0xF7,0xFF,0xBF,0xFD,0xFF,0xEF,0xFF,0x7F,0xFB,0xFF,0xDF,0xFE,0xFF,0xF7,0xFF,0xBF,0xFD,0xFF,0xEF,0xFF,0x7F,0xFB,0xFF,0xDF,0xFE,0xFF,0xF7,0xFF,0xBF,0xFD,0xFF,0xEF,0xFF,0x7F,0xFB,0xFF,0xDF,0xFE,0xFF,0xF7,0xFF,0xBF,0xFD,0xFF,0xEF,0xFF,0x7F,0xFB,0xFF,0xDF,0xFE,0xFF,0xF7,0xFF,0xBF,0xFD,0xFF,0xEF,0xFF,0x7F,0xFB,0xFF,0xDF,0xFE,0xFF,0xF7,0xFF,0xBF,0xFD,0xFF,0xEF,0xFF,0x7F,0xFB,0xFF,0xDF,0xFE,0xFF,0xF7,0xFF,0x80, // 'I'
	0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0x7F,0xF8,0x00,0x00,0x0F,0xFF,0x00,0x00,0x01,0xFF,0xE0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x07,0xFF,0x80,0x00,0x00,0xFF,0xF0,0x00,0x00,0x1F,0xFE,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0x7F,0xF8,0x00,0x00,0x0F,0xFF,0x00,0x00,0x01,0xFF,0xE0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x07,0xFF,0x80,0x00,0x00,0xFF,0xF0,0x00,0x00,0x1F,0xFE,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0x7F,0xF8,0x00,0x00,0x0F,0xFF,0x00,0x00,0x01,0xFF,0xE0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x07,0xFF,0x80,0x00,0x00,0xFF,0xF0,0x00,0x00,0x1F,0xFE,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0x7F,0xF8,0x00,0x00,0x0F,0xFF,0x00,0x00,0x01,0xFF,0xE0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x07,0xFF,0x80,0x00,0x00,0xFF,0xF0,0x00,0x00,0x1F,0xFE,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0x7F,0xF8,0x00,0x00,0x0F,0xFF,0x7F,0xF8,0x01,0xFF,0xEF,0xFF,0x00,0x3F,0xFD,0xFF,0xE0,0x07,0xFF,0xBF,0xFC,0x00,0xFF,0xF7,0xFF,0xC0,0x3F,0xFE,0x7F,0xF8,0x07,0xFF,0x8F,0xFF,0xC3,0xFF,0xF1,0xFF,0xFF,0xFF,0xFE,0x1F,0xFF,0xFF,0xFF,0x83,0xFF,0xFF,0xFF,0xE0,0x3F,0xFF,0xFF,0xF8,0x03,0xFF,0xFF,0xFE,0x00,0x3F,0xFF,0xFF,0x80,0x01,0xFF,0xFF,0xE0,0x00,0x0F,0xFF,0xF0,0x00,0x00,0x3F,0xF0,0x00,0x00, // 'J'
	0xFF,0xF0,0x00,0x3F,0xFF,0xDF,0xFE,0x00,0x07,0xFF,0xE3,0xFF,0xC0,0x01,0xFF,0xFC,0x7F,0xF8,0x00,0x7F,0xFF,0x0F,0xFF,0x00,0x0F,0xFF,0xC1,0xFF,0xE0,0x03,0xFF,0xF0,0x3F,0xFC,0x00,0xFF,0xFE,0x07,0xFF,0x80,0x1F,0xFF,0x80,0xFF,0xF0,0x07,0xFF,0xE0,0x1F,0xFE,0x01,0xFF,0xF8,0x03,0xFF,0xC0,0x3F,0xFF,0x00,0x7F,0xF8,0x0F,0xFF,0xC0,0x0F,0xFF,0x03,0xFF,0xF0,0x01,0xFF,0xE0,0x7F,0xFC,0x00,0x3F,0xFC,0x1F,0xFF,0x80,0x07,0xFF,0x87,0xFF,0xE0,0x00,0xFF,0xF1,0xFF,0xF8,0x00,0x1F,0xFE,0x3F,0xFE,0x00,0x03,0xFF,0xCF,0xFF,0xC0,0x00,0x7F,0xF9,0xFF,0xF0,0x00,0x0F,0xFF,0x7F,0xFC,0x00,0x01,0xFF,0xFF,0xFF,0x00,0x00,0x3F,0xFF,0xFF,0xE0,0x00,0x07,0xFF,0xFF,0xFE,0x00,0x00,0xFF,0xFF,0xFF,0xC0,0x00,0x1F,0xFF,0xFF,0xFC,0x00,0x03,0xFF,0xFF,0xFF,0x80,0x00,0x7F,0xFF,0xFF,0xF8,0x00,0x0F,0xFF,0xFF,0xFF,0x80,0x01,0xFF,0xFF,0xFF,0xF0,0x00,0x3F,0xFF,0xFF,0xFF,0x00,0x07,0xFF,0xFF,0xFF,0xE0,0x00,0xFF,0xFF,0x7F,0xFE,0x00,0x1F,0xFF,0xC7,0xFF,0xE0,0x03,0xFF,0xF0,0x7F,0xFC,0x00,0x7F,0xFC,0x0F,0xFF,0xC0,0x0F,0xFF,0x00,0xFF,0xF8,0x01,0xFF,0xE0,0x1F,0xFF,0x80,0x3F,0xFC,0x01,0xFF,0xF8,0x07,0xFF,0x80,0x3F,0xFF,0x00,0xFF,0xF0,0x03,0xFF,0xF0,0x1F,0xFE,0x00,0x3F,0xFE,0x03,0xFF,0xC0,0x07,0xFF,0xE0,0x7F,0xF8,0x00,0x7F,0xFE,0x0F,0xFF,0x00,0x0F,0xFF,0xC1,0xFF,0xE0,0x00,0xFF,0xFC,0x3F,0xFC,0x00,0x1F,0xFF,0x87,0xFF,0x80,0x01,0xFF,0xF8,0xFF,0xF0,0x00,0x1F,0xFF,0x9F,0xFE,0x00,0x03,0xFF,0xF8, // 'K'
	0xFF,0xF0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x0F,0xFF,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0xFF,0xF0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x0F,0xFF,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0xFF,0xF0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x0F,0xFF,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0xFF,0xF0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x0F,0xFF,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0xFF,0xF0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x0F,0xFF,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0xFF,0xF0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x0F,0xFF,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0xFF,0xF0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x0F,0xFF,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0xFF,0xF0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x0F,0xFF,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0xFF,0xF0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x0F,0xFF,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0xFF,0xF0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x0F,0xFF,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0xFF,0xF0,0x00,0x00,0x3F,0xFF,0xFF,0xFF,0xEF,0xFF,0xFF,0xFF,0xFB,0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,0xFF,0xFF,0xBF,0xFF,0xFF,0xFF,0xEF,0xFF,0xFF,0xFF,0xFB,0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,0xFF,0xFF,0xBF,0xFF,0xFF,0xFF,0xE0, // 'L'
	0xFF,0xFF,0x00,0x00,0x03,0xFF,0xFB,0xFF,0xFC,0x00,0x00,0x1F,0xFF,0xEF,0xFF,0xF0,0x00,0x00,0x7F,0xFF,0xBF,0xFF,0xE0,0x00,0x01,0xFF,0xFE,0xFF,0xFF,0x80,0x00,0x0F,0xFF,0xFB,0xFF,0xFE,0x00,0x00,0x3F,0xFF,0xEF,0xFF,0xFC,0x00,0x00,0xFF,0xFF,0xBF,0xFF,0xF0,0x00,0x07,0xFF,0xFE,0xFF,0xFF,0xC0,0x00,0x1F,0xFF,0xFB,0xFF,0xFF,0x80,0x00,0x7F,0xFF,0xEF,0xFF,0xFE,0x00,0x03,0xFF,0xFF,0xBF,0xFF,0xF8,0x00,0x0F,0xFF,0xFE,0xFF,0xFF,0xF0,0x00,0x3F,0xFF,0xFB,0xFF,0xFF,0xC0,0x01,0xFF,0xFF,0xEF,0xFF,0xFF,0x00,0x07,0xFF,0xFF,0xBF,0xFF,0xFC,0x00,0x1F,0xF7,0xFE,0xFF,0xFF,0xF8,0x00,0xFF,0xDF,0xFB,0xFF,0xBF,0xE0,0x03,0xFF,0xFF,0xEF,0xFE,0xFF,0x80,0x0F,0xFB,0xFF,0xBF,0xFB,0xFF,0x00,0x7F,0xEF,0xFE,0xFF,0xE7,0xFC,0x01,0xFF,0xBF,0xFB,0xFF,0x9F,0xF0,0x07,0xFC,0xFF,0xEF,0xFE,0x7F,0xE0,0x1F,0xF3,0xFF,0xBF,0xF8,0xFF,0x80,0xFF,0xCF,0xFE,0xFF,0xE3,0xFE,0x03,0xFE,0x3F,0xFB,0xFF,0x8F,0xFC,0x0F,0xF8,0xFF,0xEF,0xFE,0x1F,0xF0,0x7F,0xE3,0xFF,0xBF,0xF8,0x7F,0xC1,0xFF,0x0F,0xFE,0xFF,0xE1,0xFF,0x87,0xFC,0x3F,0xFB,0xFF,0x83,0xFE,0x3F,0xF0,0xFF,0xEF,0xFE,0x0F,0xF8,0xFF,0x83,0xFF,0xBF,0xF8,0x3F,0xF3,0xFE,0x0F,0xFE,0xFF,0xE0,0x7F,0xDF,0xF8,0x3F,0xFB,0xFF,0xC1,0xFF,0x7F,0xC0,0xFF,0xEF,0xFF,0x07,0xFF,0xFF,0x03,0xFF,0xBF,0xFC,0x0F,0xFF,0xFC,0x0F,0xFE,0xFF,0xF0,0x3F,0xFF,0xE0,0x3F,0xFB,0xFF,0xC0,0xFF,0xFF,0x81,0xFF,0xEF,0xFF,0x01,0xFF,0xFC,0x07,0xFF,0xBF,0xFC,0x07,0xFF,0xF0,0x1F,0xFE,0xFF,0xF0,0x1F,0xFF,0xC0,0x7F,0xFB,0xFF,0xC0,0x3F,0xFE,0x01,0xFF,0xEF,0xFF,0x00,0xFF,0xF8,0x07,0xFF,0xBF,0xFC,0x03,0xFF,0xE0,0x1F,0xFE,0xFF,0xF0,0x07,0xFF,0x00,0x7F,0xFB,0xFF,0xC0,0x1F,0xFC,0x01,0xFF,0xEF,0xFF,0x00,0x7F,0xF0,0x07,0xFF,0xBF,0xFC,0x00,0xFF,0x80,0x1F,0xFE,0xFF,0xF0,0x03,0xFE,0x00,0x7F,0xFB,0xFF,0xC0,0x0F,0xF8,0x01,0xFF,0xE0, // 'M'
	0xFF,0xF0,0x00,0x07,0xFF,0xBF,0xFE,0x00,0x01,0xFF,0xEF,0xFF,0x80,0x00,0x7F,0xFB,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xFC,0x00,0x07,0xFF,0xBF,0xFF,0x80,0x01,0xFF,0xEF,0xFF,0xE0,0x00,0x7F,0xFB,0xFF,0xFC,0x00,0x1F,0xFE,0xFF,0xFF,0x00,0x07,0xFF,0xBF,0xFF,0xE0,0x01,0xFF,0xEF,0xFF,0xFC,0x00,0x7F,0xFB,0xFF,0xFF,0x00,0x1F,0xFE,0xFF,0xFF,0xE0,0x07,0xFF,0xBF,0xFF,0xF8,0x01,0xFF,0xEF,0xFF,0xFF,0x00,0x7F,0xFB,0xFF,0xFF,0xC0,0x1F,0xFE,0xFF,0xFF,0xF8,0x07,0xFF,0xBF,0xFF,0xFF,0x01,0xFF,0xEF,0xFF,0xFF,0xC0,0x7F,0xFB,0xFF,0xFF,0xF8,0x1F,0xFE,0xFF,0xF7,0xFE,0x07,0xFF,0xBF,0xFD,0xFF,0xC1,0xFF,0xEF,0xFF,0x3F,0xF0,0x7F,0xFB,0xFF,0xCF,0xFE,0x1F,0xFE,0xFF,0xF1,0xFF,0xC7,0xFF,0xBF,0xFC,0x7F,0xF1,0xFF,0xEF,0xFF,0x0F,0xFE,0x7F,0xFB,0xFF,0xC1,0xFF,0x9F,0xFE,0xFF,0xF0,0x7F,0xF7,0xFF,0xBF,0xFC,0x0F,0xFD,0xFF,0xEF,0xFF,0x03,0xFF,0xFF,0xFB,0xFF,0xC0,0x7F,0xFF,0xFE,0xFF,0xF0,0x1F,0xFF,0xFF,0xBF,0xFC,0x03,0xFF,0xFF,0xEF,0xFF,0x00,0x7F,0xFF,0xFB,0xFF,0xC0,0x1F,0xFF,0xFE,0xFF,0xF0,0x03,0xFF,0xFF,0xBF,0xFC,0x00,0xFF,0xFF,0xEF,0xFF,0x00,0x1F,0xFF,0xFB,0xFF,0xC0,0x07,0xFF,0xFE,0xFF,0xF0,0x00,0xFF,0xFF,0xBF,0xFC,0x00,0x3F,0xFF,0xEF,0xFF,0x00,0x07,0xFF,0xFB,0xFF,0xC0,0x00,0xFF,0xFE,0xFF,0xF0,0x00,0x3F,0xFF,0xBF,0xFC,0x00,0x07,0xFF,0xEF,0xFF,0x00,0x01,0xFF,0xFB,0xFF,0xC0,0x00,0x3F,0xFE,0xFF,0xF0,0x00,0x0F,0xFF,0xBF,0xFC,0x00,0x01,0xFF,0xE0, // 'N'
	0x00,0x00,0x7F,0xE0,0x00,0x00,0x00,0x1F,0xFF,0xE0,0x00,0x00,0x07,0xFF,0xFF,0xC0,0x00,0x00,0x7F,0xFF,0xFF,0x80,0x00,0x0F,0xFF,0xFF,0xFE,0x00,0x00,0xFF,0xFF,0xFF,0xFC,0x00,0x0F,0xFF,0xFF,0xFF,0xF0,0x00,0xFF,0xFF,0xFF,0xFF,0x80,0x07,0xFF,0xFF,0xFF,0xFE,0x00,0x7F,0xFF,0x03,0xFF,0xF8,0x07,0xFF,0xE0,0x07,0xFF,0xC0,0x3F,0xFE,0x00,0x1F,0xFF,0x03,0xFF,0xE0,0x00,0x7F,0xF8,0x1F,0xFE,0x00,0x03,0xFF,0xE0,0xFF,0xF0,0x00,0x0F,0xFF,0x0F,0xFF,0x80,0x00,0x7F,0xF8,0x7F,0xF8,0x00,0x01,0xFF,0xE3,0xFF,0xC0,0x00,0x0F,0xFF,0x1F,0xFE,0x00,0x00,0x7F,0xF8,0xFF,0xF0,0x00,0x03,0xFF,0xCF,0xFF,0x80,0x00,0x1F,0xFF,0x7F,0xFC,0x00,0x00,0xFF,0xFB,0xFF,0xC0,0x00,0x07,0xFF,0xDF,0xFE,0x00,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x00,0xFF,0xF7,0xFF,0x80,0x00,0x07,0xFF,0xBF,0xFC,0x00,0x00,0x3F,0xFD,0xFF,0xE0,0x00,0x01,0xFF,0xEF,0xFF,0x00,0x00,0x1F,0xFF,0x7F,0xF8,0x00,0x00,0xFF,0xFB,0xFF,0xE0,0x00,0x07,0xFF,0xCF,0xFF,0x00,0x00,0x3F,0xFC,0x7F,0xF8,0x00,0x01,0xFF,0xE3,0xFF,0xC0,0x00,0x0F,0xFF,0x1F,0xFE,0x00,0x00,0x7F,0xF8,0xFF,0xF8,0x00,0x07,0xFF,0xC3,0xFF,0xC0,0x00,0x3F,0xFC,0x1F,0xFF,0x00,0x03,0xFF,0xE0,0xFF,0xF8,0x00,0x1F,0xFE,0x03,0xFF,0xE0,0x01,0xFF,0xF0,0x1F,0xFF,0x80,0x1F,0xFF,0x00,0x7F,0xFF,0x03,0xFF,0xF8,0x01,0xFF,0xFF,0xFF,0xFF,0x80,0x0F,0xFF,0xFF,0xFF,0xF8,0x00,0x3F,0xFF,0xFF,0xFF,0xC0,0x00,0xFF,0xFF,0xFF,0xFC,0x00,0x03,0xFF,0xFF,0xFF,0xC0,0x00,0x07,0xFF,0xFF,0xF8,0x00,0x00,0x1F,0xFF,0xFF,0x80,0x00,0x00,0x1F,0xFF,0xE0,0x00,0x00,0x00,0x1F,0xF8,0x00,0x00, // 'O'
	0xFF,0xFF,0xFF,0x80,0x00,0xFF,0xFF,0xFF,0xF0,0x00,0xFF,0xFF,0xFF,0xFE,0x00,0xFF,0xFF,0xFF,0xFF,0x00,0xFF,0xFF,0xFF,0xFF,0xC0,0xFF,0xFF,0xFF,0xFF,0xE0,0xFF,0xFF,0xFF,0xFF,0xF0,0xFF,0xFF,0xFF,0xFF,0xF0,0xFF,0xFF,0xFF,0xFF,0xF8,0xFF,0xF0,0x01,0xFF,0xF8,0xFF,0xF0,0x00,0xFF,0xFC,0xFF,0xF0,0x00,0x7F,0xFC,0xFF,0xF0,0x00,0x3F,0xFE,0xFF,0xF0,0x00,0x3F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x3F,0xFE,0xFF,0xF0,0x00,0x3F,0xFE,0xFF,0xF0,0x00,0x7F,0xFC,0xFF,0xF0,0x01,0xFF,0xFC,0xFF,0xFF,0xFF,0xFF,0xF8,0xFF,0xFF,0xFF,0xFF,0xF8,0xFF,0xFF,0xFF,0xFF,0xF0,0xFF,0xFF,0xFF,0xFF,0xE0,0xFF,0xFF,0xFF,0xFF,0xC0,0xFF,0xFF,0xFF,0xFF,0x80,0xFF,0xFF,0xFF,0xFE,0x00,0xFF,0xFF,0xFF,0xF8,0x00,0xFF,0xFF,0xFF,0x80,0x00,0xFF,0xF0,0x00,0x00,0x00,0xFF,0xF0,0x00,0x00,0x00,0xFF,0xF0,0x00,0x00,0x00,0xFF,0xF0,0x00,0x00,0x00,0xFF,0xF0,0x00,0x00,0x00,0xFF,0xF0,0x00,0x00,0x00,0xFF,0xF0,0x00,0x00,0x00,0xFF,0xF0,0x00,0x00,0x00,0xFF,0xF0,0x00,0x00,0x00,0xFF,0xF0,0x00,0x00,0x00,0xFF,0xF0,0x00,0x00,0x00,0xFF,0xF0,0x00,0x00,0x00,0xFF,0xF0,0x00,0x00,0x00,0xFF,0xF0,0x00,0x00,0x00,0xFF,0xF0,0x00,0x00,0x00,0xFF,0xF0,0x00,0x00,0x00,0xFF,0xF0,0x00,0x00,0x00, // 'P'
	0x00,0x00,0x7F,0xE0,0x00,0x00,0x00,0x1F,0xFF,0xE0,0x00,0x00,0x03,0xFF,0xFF,0xC0,0x00,0x00,0x7F,0xFF,0xFF,0x80,0x00,0x07,0xFF,0xFF,0xFE,0x00,0x00,0xFF,0xFF,0xFF,0xFC,0x00,0x0F,0xFF,0xFF,0xFF,0xF0,0x00,0x7F,0xFF,0xFF,0xFF,0x80,0x07,0xFF,0xFF,0xFF,0xFE,0x00,0x7F,0xFF,0x03,0xFF,0xF8,0x03,0xFF,0xE0,0x07,0xFF,0xC0,0x3F,0xFE,0x00,0x1F,0xFF,0x01,0xFF,0xE0,0x00,0x7F,0xF8,0x1F,0xFF,0x00,0x03,0xFF,0xE0,0xFF,0xF0,0x00,0x0F,0xFF,0x0F,0xFF,0x80,0x00,0x7F,0xFC,0x7F,0xF8,0x00,0x01,0xFF,0xE3,0xFF,0xC0,0x00,0x0F,0xFF,0x1F,0xFE,0x00,0x00,0x7F,0xF8,0xFF,0xF0,0x00,0x03,0xFF,0xCF,0xFF,0x80,0x00,0x1F,0xFF,0x7F,0xFC,0x00,0x00,0xFF,0xFB,0xFF,0xC0,0x00,0x03,0xFF,0xDF,0xFE,0x00,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x00,0xFF,0xF7,0xFF,0x80,0x00,0x07,0xFF,0xBF,0xFC,0x00,0x00,0x3F,0xFD,0xFF,0xE0,0x00,0x01,0xFF,0xEF,0xFF,0x00,0x00,0x0F,0xFF,0x7F,0xFC,0x00,0x00,0xFF,0xFB,0xFF,0xE0,0x00,0x07,0xFF,0xCF,0xFF,0x00,0x00,0x3F,0xFC,0x7F,0xF8,0x00,0x01,0xFF,0xE3,0xFF,0xC0,0x00,0x0F,0xFF,0x1F,0xFE,0x00,0x00,0x7F,0xF8,0xFF,0xF8,0x00,0x07,0xFF,0xC3,0xFF,0xC0,0x00,0x3F,0xFC,0x1F,0xFF,0x00,0x03,0xFF,0xE0,0x7F,0xF8,0x00,0x1F,0xFF,0x03,0xFF,0xE0,0x01,0xFF,0xF0,0x0F,0xFF,0x80,0x1F,0xFF,0x80,0x7F,0xFF,0x03,0xFF,0xF8,0x01,0xFF,0xFF,0xFF,0xFF,0x80,0x07,0xFF,0xFF,0xFF,0xFC,0x00,0x3F,0xFF,0xFF,0xFF,0xC0,0x00,0xFF,0xFF,0xFF,0xFC,0x00,0x01,0xFF,0xFF,0xFF,0xC0,0x00,0x07,0xFF,0xFF,0xFE,0x00,0x00,0x0F,0xFF,0xFF,0xF8,0x00,0x00,0x1F,0xFF,0xFF,0xF0,0x00,0x00,0x1F,0xFF,0xFF,0xC0,0x00,0x00,0x00,0x7F,0xFF,0x00,0x00,0x00,0x01,0xFF,0xFC,0x00,0x00,0x00,0x07,0xFF,0xF0,0x00,0x00,0x00,0x0F,0xFF,0x00,0x00,0x00,0x00,0x3F,0xE0,0x00,0x00,0x00,0x00,0xFE,0x00,0x00,0x00,0x00,0x03,0xE0,0x00,0x00,0x00,0x00,0x06,0x00, // 'Q'
	0xFF,0xFF,0xFF,0x80,0x00,0x7F,0xFF,0xFF,0xFC,0x00,0x3F,0xFF,0xFF,0xFF,0x80,0x1F,0xFF,0xFF,0xFF,0xF0,0x0F,0xFF,0xFF,0xFF,0xFC,0x07,0xFF,0xFF,0xFF,0xFF,0x03,0xFF,0xFF,0xFF,0xFF,0xC1,0xFF,0xFF,0xFF,0xFF,0xE0,0xFF,0xFF,0xFF,0xFF,0xF8,0x7F,0xF8,0x00,0xFF,0xFC,0x3F,0xFC,0x00,0x3F,0xFF,0x1F,0xFE,0x00,0x0F,0xFF,0x8F,0xFF,0x00,0x03,0xFF,0xC7,0xFF,0x80,0x01,0xFF,0xE3,0xFF,0xC0,0x00,0xFF,0xF1,0xFF,0xE0,0x00,0x7F,0xF8,0xFF,0xF0,0x00,0x3F,0xFC,0x7F,0xF8,0x00,0x1F,0xFE,0x3F,0xFC,0x00,0x0F,0xFF,0x1F,0xFE,0x00,0x07,0xFF,0x8F,0xFF,0x00,0x07,0xFF,0xC7,0xFF,0x80,0x07,0xFF,0xC3,0xFF,0xC0,0x07,0xFF,0xE1,0xFF,0xFF,0xFF,0xFF,0xE0,0xFF,0xFF,0xFF,0xFF,0xF0,0x7F,0xFF,0xFF,0xFF,0xF0,0x3F,0xFF,0xFF,0xFF,0xF0,0x1F,0xFF,0xFF,0xFF,0xE0,0x0F,0xFF,0xFF,0xFF,0xE0,0x07,0xFF,0xFF,0xFF,0xE0,0x03,0xFF,0xFF,0xFF,0xF0,0x01,0xFF,0xFF,0xFF,0xFC,0x00,0xFF,0xF0,0x1F,0xFE,0x00,0x7F,0xF8,0x0F,0xFF,0x80,0x3F,0xFC,0x03,0xFF,0xC0,0x1F,0xFE,0x01,0xFF,0xF0,0x0F,0xFF,0x00,0x7F,0xF8,0x07,0xFF,0x80,0x3F,0xFE,0x03,0xFF,0xC0,0x0F,0xFF,0x01,0xFF,0xE0,0x07,0xFF,0xC0,0xFF,0xF0,0x01,0xFF,0xF0,0x7F,0xF8,0x00,0xFF,0xF8,0x3F,0xFC,0x00,0x3F,0xFE,0x1F,0xFE,0x00,0x1F,0xFF,0x0F,0xFF,0x00,0x07,0xFF,0xC7,0xFF,0x80,0x03,0xFF,0xE3,0xFF,0xC0,0x01,0xFF,0xF9,0xFF,0xE0,0x00,0x7F,0xFC,0xFF,0xF0,0x00,0x3F,0xFF,0x7F,0xF8,0x00,0x0F,0xFF,0x80, // 'R'
	0x00,0x01,0xFF,0xC0,0x00,0x00,0x07,0xFF,0xFC,0x00,0x00,0x1F,0xFF,0xFF,0xC0,0x00,0x1F,0xFF,0xFF,0xF0,0x00,0x3F,0xFF,0xFF,0xFE,0x00,0x3F,0xFF,0xFF,0xFF,0x80,0x3F,0xFF,0xFF,0xFF,0xC0,0x1F,0xFF,0xFF,0xFF,0xF0,0x1F,0xFF,0xFF,0xFF,0xFC,0x0F,0xFF,0xC0,0x7F,0xFE,0x0F,0xFF,0xC0,0x1F,0xFF,0x87,0xFF,0xC0,0x07,0xFF,0xC3,0xFF,0xC0,0x01,0xFF,0xE1,0xFF,0xE0,0x00,0xFF,0xF0,0xFF,0xF0,0x00,0x7F,0xF8,0x7F,0xF8,0x00,0x00,0x02,0x3F,0xFE,0x00,0x00,0x00,0x1F,0xFF,0x80,0x00,0x00,0x07,0xFF,0xE0,0x00,0x00,0x03,0xFF,0xFC,0x00,0x00,0x00,0xFF,0xFF,0xC0,0x00,0x00,0x7F,0xFF,0xFC,0x00,0x00,0x1F,0xFF,0xFF,0x80,0x00,0x07,0xFF,0xFF,0xF8,0x00,0x01,0xFF,0xFF,0xFF,0x00,0x00,0x3F,0xFF,0xFF,0xC0,0x00,0x0F,0xFF,0xFF,0xF0,0x00,0x00,0xFF,0xFF,0xFC,0x00,0x00,0x1F,0xFF,0xFF,0x00,0x00,0x01,0xFF,0xFF,0xC0,0x00,0x00,0x3F,0xFF,0xF0,0x00,0x00,0x03,0xFF,0xF8,0x00,0x00,0x00,0x7F,0xFE,0x00,0x00,0x00,0x1F,0xFF,0x3F,0xFC,0x00,0x07,0xFF,0x9F,0xFE,0x00,0x03,0xFF,0xCF,0xFF,0x00,0x01,0xFF,0xE3,0xFF,0xC0,0x00,0xFF,0xF9,0xFF,0xE0,0x00,0x7F,0xF8,0xFF,0xF8,0x00,0x3F,0xFC,0x7F,0xFE,0x00,0x3F,0xFE,0x1F,0xFF,0xC0,0x7F,0xFF,0x0F,0xFF,0xFF,0xFF,0xFF,0x03,0xFF,0xFF,0xFF,0xFF,0x80,0xFF,0xFF,0xFF,0xFF,0x80,0x3F,0xFF,0xFF,0xFF,0xC0,0x0F,0xFF,0xFF,0xFF,0xC0,0x01,0xFF,0xFF,0xFF,0x80,0x00,0x7F,0xFF,0xFF,0x80,0x00,0x07,0xFF,0xFF,0x00,0x00,0x00,0x3F,0xF8,0x00,0x00, // 'S'
	0xFF,0xFF,0xFF,0xFF,0xFF,0xDF,0xFF,0xFF,0xFF,0xFF,0xFB,0xFF,0xFF,0xFF,0xFF,0xFF,0x7F,0xFF,0xFF,0xFF,0xFF,0xEF,0xFF,0xFF,0xFF,0xFF,0xFD,0xFF,0xFF,0xFF,0xFF,0xFF,0xBF,0xFF,0xFF,0xFF,0xFF,0xF7,0xFF,0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,0xFF,0xFF,0xFF,0xC0,0x00,0x3F,0xFC,0x00,0x00,0x00,0x07,0xFF,0x80,0x00,0x00,0x00,0xFF,0xF0,0x00,0x00,0x00,0x1F,0xFE,0x00,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0x00,0x7F,0xF8,0x00,0x00,0x00,0x0F,0xFF,0x00,0x00,0x00,0x01,0xFF,0xE0,0x00,0x00,0x00,0x3F,0xFC,0x00,0x00,0x00,0x07,0xFF,0x80,0x00,0x00,0x00,0xFF,0xF0,0x00,0x00,0x00,0x1F,0xFE,0x00,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0x00,0x7F,0xF8,0x00,0x00,0x00,0x0F,0xFF,0x00,0x00,0x00,0x01,0xFF,0xE0,0x00,0x00,0x00,0x3F,0xFC,0x00,0x00,0x00,0x07,0xFF,0x80,0x00,0x00,0x00,0xFF,0xF0,0x00,0x00,0x00,0x1F,0xFE,0x00,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0x00,0x7F,0xF8,0x00,0x00,0x00,0x0F,0xFF,0x00,0x00,0x00,0x01,0xFF,0xE0,0x00,0x00,0x00,0x3F,0xFC,0x00,0x00,0x00,0x07,0xFF,0x80,0x00,0x00,0x00,0xFF,0xF0,0x00,0x00,0x00,0x1F,0xFE,0x00,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0x00,0x7F,0xF8,0x00,0x00,0x00,0x0F,0xFF,0x00,0x00,0x00,0x01,0xFF,0xE0,0x00,0x00,0x00,0x3F,0xFC,0x00,0x00,0x00,0x07,0xFF,0x80,0x00,0x00,0x00,0xFF,0xF0,0x00,0x00,0x00,0x1F,0xFE,0x00,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0x00,0x7F,0xF8,0x00,0x00,0x00,0x0F,0xFF,0x00,0x00,0x00,0x01,0xFF,0xE0,0x00,0x00,0x00,0x3F,0xFC,0x00,0x00, // 'T'
	0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0x3F,0xFE,0x7F,0xF8,0x00,0x3F,0xFC,0x7F,0xF8,0x00,0x7F,0xFC,0x7F,0xFC,0x00,0xFF,0xFC,0x3F,0xFF,0x01,0xFF,0xF8,0x3F,0xFF,0xFF,0xFF,0xF8,0x1F,0xFF,0xFF,0xFF,0xF0,0x1F,0xFF,0xFF,0xFF,0xE0,0x0F,0xFF,0xFF,0xFF,0xE0,0x07,0xFF,0xFF,0xFF,0xC0,0x01,0xFF,0xFF,0xFF,0x00,0x00,0xFF,0xFF,0xFE,0x00,0x00,0x3F,0xFF,0xF0,0x00,0x00,0x03,0xFF,0x80,0x00, // 'U'
	0xFF,0xF8,0x00,0x00,0x7F,0xFD,0xFF,0xF0,0x00,0x01,0xFF,0xF9,0xFF,0xF0,0x00,0x03,0xFF,0xE3,0xFF,0xE0,0x00,0x07,0xFF,0xC7,0xFF,0xC0,0x00,0x0F,0xFF,0x87,0xFF,0xC0,0x00,0x3F,0xFE,0x0F,0xFF,0x80,0x00,0x7F,0xFC,0x1F,0xFF,0x00,0x00,0xFF,0xF8,0x1F,0xFE,0x00,0x01,0xFF,0xE0,0x3F,0xFE,0x00,0x07,0xFF,0xC0,0x7F,0xFC,0x00,0x0F,0xFF,0x80,0x7F,0xF8,0x00,0x1F,0xFE,0x00,0xFF,0xF0,0x00,0x7F,0xFC,0x01,0xFF,0xF0,0x00,0xFF,0xF8,0x01,0xFF,0xE0,0x01,0xFF,0xE0,0x03,0xFF,0xC0,0x03,0xFF,0xC0,0x07,0xFF,0xC0,0x0F,0xFF,0x80,0x07,0xFF,0x80,0x1F,0xFE,0x00,0x0F,0xFF,0x00,0x3F,0xFC,0x00,0x1F,0xFE,0x00,0x7F,0xF8,0x00,0x1F,0xFE,0x01,0xFF,0xE0,0x00,0x3F,0xFC,0x03,0xFF,0xC0,0x00,0x7F,0xF8,0x07,0xFF,0x80,0x00,0x7F,0xF0,0x1F,0xFE,0x00,0x00,0xFF,0xF0,0x3F,0xFC,0x00,0x01,0xFF,0xE0,0x7F,0xF8,0x00,0x01,0xFF,0xC0,0xFF,0xE0,0x00,0x03,0xFF,0xC3,0xFF,0xC0,0x00,0x07,0xFF,0x87,0xFF,0x80,0x00,0x07,0xFF,0x0F,0xFE,0x00,0x00,0x0F,0xFE,0x1F,0xFC,0x00,0x00,0x1F,0xFE,0x7F,0xF8,0x00,0x00,0x1F,0xFC,0xFF,0xE0,0x00,0x00,0x3F,0xF9,0xFF,0xC0,0x00,0x00,0x7F,0xF7,0xFF,0x80,0x00,0x00,0x7F,0xFF,0xFE,0x00,0x00,0x00,0xFF,0xFF,0xFC,0x00,0x00,0x01,0xFF,0xFF,0xF8,0x00,0x00,0x01,0xFF,0xFF,0xE0,0x00,0x00,0x03,0xFF,0xFF,0xC0,0x00,0x00,0x07,0xFF,0xFF,0x80,0x00,0x00,0x07,0xFF,0xFE,0x00,0x00,0x00,0x0F,0xFF,0xFC,0x00,0x00,0x00,0x1F,0xFF,0xF8,0x00,0x00,0x00,0x1F,0xFF,0xE0,0x00,0x00,0x00,0x3F,0xFF,0xC0,0x00,0x00,0x00,0x7F,0xFF,0x80,0x00,0x00,0x00,0x7F,0xFE,0x00,0x00,0x00,0x00,0xFF,0xFC,0x00,0x00,0x00,0x01,0xFF,0xF8,0x00,0x00, // 'V'
	0xFF,0xF0,0x00,0xFF,0xE0,0x01,0xFF,0xEF,0xFF,0x00,0x0F,0xFE,0x00,0x1F,0xFE,0xFF,0xF0,0x00,0xFF,0xE0,0x01,0xFF,0xC7,0xFF,0x00,0x1F,0xFE,0x00,0x3F,0xFC,0x7F,0xF8,0x01,0xFF,0xE0,0x03,0xFF,0xC7,0xFF,0x80,0x1F,0xFF,0x00,0x3F,0xFC,0x7F,0xF8,0x01,0xFF,0xF0,0x03,0xFF,0xC7,0xFF,0x80,0x1F,0xFF,0x00,0x3F,0xF8,0x3F,0xF8,0x03,0xFF,0xF0,0x03,0xFF,0x83,0xFF,0x80,0x3F,0xFF,0x80,0x7F,0xF8,0x3F,0xFC,0x03,0xFF,0xF8,0x07,0xFF,0x83,0xFF,0xC0,0x3F,0xFF,0x80,0x7F,0xF8,0x3F,0xFC,0x03,0xFF,0xF8,0x07,0xFF,0x01,0xFF,0xC0,0x7F,0xFF,0x80,0x7F,0xF0,0x1F,0xFC,0x07,0xFF,0xFC,0x0F,0xFF,0x01,0xFF,0xE0,0x7F,0xFF,0xC0,0xFF,0xF0,0x1F,0xFE,0x07,0xFF,0xFC,0x0F,0xFF,0x00,0xFF,0xE0,0xFF,0xFF,0xC0,0xFF,0xE0,0x0F,0xFE,0x0F,0xFF,0xFE,0x0F,0xFE,0x00,0xFF,0xE0,0xFF,0xFF,0xE0,0xFF,0xE0,0x0F,0xFE,0x0F,0xFF,0xFE,0x1F,0xFE,0x00,0xFF,0xF0,0xFF,0xFF,0xE1,0xFF,0xC0,0x07,0xFF,0x1F,0xFB,0xFE,0x1F,0xFC,0x00,0x7F,0xF1,0xFF,0xBF,0xF1,0xFF,0xC0,0x07,0xFF,0x1F,0xFB,0xFF,0x1F,0xFC,0x00,0x7F,0xF1,0xFF,0x3F,0xF3,0xFF,0xC0,0x07,0xFF,0xBF,0xF1,0xFF,0x3F,0xF8,0x00,0x3F,0xFB,0xFF,0x1F,0xFB,0xFF,0x80,0x03,0xFF,0xBF,0xF1,0xFF,0xBF,0xF8,0x00,0x3F,0xFB,0xFF,0x1F,0xFB,0xFF,0x80,0x03,0xFF,0xBF,0xE1,0xFF,0xBF,0xF8,0x00,0x3F,0xFF,0xFE,0x0F,0xFF,0xFF,0x00,0x01,0xFF,0xFF,0xE0,0xFF,0xFF,0xF0,0x00,0x1F,0xFF,0xFE,0x0F,0xFF,0xFF,0x00,0x01,0xFF,0xFF,0xC0,0xFF,0xFF,0xF0,0x00,0x1F,0xFF,0xFC,0x07,0xFF,0xFF,0x00,0x00,0xFF,0xFF,0xC0,0x7F,0xFF,0xE0,0x00,0x0F,0xFF,0xFC,0x07,0xFF,0xFE,0x00,0x00,0xFF,0xFF,0x80,0x7F,0xFF,0xE0,0x00,0x0F,0xFF,0xF8,0x03,0xFF,0xFE,0x00,0x00,0xFF,0xFF,0x80,0x3F,0xFF,0xC0,0x00,0x07,0xFF,0xF8,0x03,0xFF,0xFC,0x00,0x00,0x7F,0xFF,0x80,0x3F,0xFF,0xC0,0x00,0x07,0xFF,0xF0,0x03,0xFF,0xFC,0x00,0x00,0x7F,0xFF,0x00,0x1F,0xFF,0xC0,0x00,0x07,0xFF,0xF0,0x01,0xFF,0xF8,0x00,0x00,0x3F,0xFF,0x00,0x1F,0xFF,0x80,0x00,0x03,0xFF,0xE0,0x01,0xFF,0xF8,0x00,0x00,0x3F,0xFE,0x00,0x0F,0xFF,0x80,0x00,0x03,0xFF,0xE0,0x00,0xFF,0xF8,0x00, // 'W'
	0x7F,0xFC,0x00,0x03,0xFF,0xF1,0xFF,0xF8,0x00,0x0F,0xFF,0x83,0xFF,0xE0,0x00,0x7F,0xFE,0x0F,0xFF,0xC0,0x01,0xFF,0xF0,0x1F,0xFF,0x00,0x0F,0xFF,0xC0,0x3F,0xFE,0x00,0x3F,0xFE,0x00,0xFF,0xF8,0x01,0xFF,0xF8,0x01,0xFF,0xF0,0x07,0xFF,0xC0,0x07,0xFF,0xC0,0x3F,0xFE,0x00,0x0F,0xFF,0x80,0xFF,0xF8,0x00,0x3F,0xFE,0x07,0xFF,0xC0,0x00,0x7F,0xFC,0x1F,0xFF,0x00,0x00,0xFF,0xF0,0xFF,0xF8,0x00,0x03,0xFF,0xE3,0xFF,0xE0,0x00,0x07,0xFF,0x9F,0xFF,0x00,0x00,0x1F,0xFF,0x7F,0xF8,0x00,0x00,0x3F,0xFF,0xFF,0xE0,0x00,0x00,0xFF,0xFF,0xFF,0x00,0x00,0x01,0xFF,0xFF,0xFC,0x00,0x00,0x07,0xFF,0xFF,0xE0,0x00,0x00,0x0F,0xFF,0xFF,0x80,0x00,0x00,0x1F,0xFF,0xFC,0x00,0x00,0x00,0x7F,0xFF,0xF0,0x00,0x00,0x00,0xFF,0xFF,0x80,0x00,0x00,0x03,0xFF,0xFC,0x00,0x00,0x00,0x0F,0xFF,0xF8,0x00,0x00,0x00,0x7F,0xFF,0xE0,0x00,0x00,0x01,0xFF,0xFF,0xC0,0x00,0x00,0x0F,0xFF,0xFF,0x00,0x00,0x00,0x3F,0xFF,0xFE,0x00,0x00,0x01,0xFF,0xFF,0xF8,0x00,0x00,0x07,0xFF,0xFF,0xF0,0x00,0x00,0x3F,0xFF,0xFF,0xE0,0x00,0x01,0xFF,0xF7,0xFF,0x80,0x00,0x07,0xFF,0xDF,0xFF,0x00,0x00,0x3F,0xFE,0x3F,0xFC,0x00,0x00,0xFF,0xF8,0xFF,0xF8,0x00,0x07,0xFF,0xC1,0xFF,0xE0,0x00,0x1F,0xFF,0x07,0xFF,0xC0,0x00,0xFF,0xF8,0x1F,0xFF,0x80,0x07,0xFF,0xE0,0x3F,0xFE,0x00,0x1F,0xFF,0x00,0xFF,0xFC,0x00,0xFF,0xFC,0x01,0xFF,0xF0,0x03,0xFF,0xE0,0x07,0xFF,0xE0,0x1F,0xFF,0x80,0x0F,0xFF,0x80,0x7F,0xFC,0x00,0x3F,0xFF,0x03,0xFF,0xF0,0x00,0x7F,0xFE,0x0F,0xFF,0x80,0x01,0xFF,0xF8,0x7F,0xFE,0x00,0x03,0xFF,0xF3,0xFF,0xF0,0x00,0x0F,0xFF,0xE0, // 'X'
	0xFF,0xF8,0x00,0x01,0xFF,0xF3,0xFF,0xC0,0x00,0x1F,0xFF,0x1F,0xFF,0x00,0x00,0xFF,0xF8,0x7F,0xF8,0x00,0x07,0xFF,0x83,0xFF,0xE0,0x00,0x7F,0xFC,0x0F,0xFF,0x00,0x03,0xFF,0xC0,0x7F,0xFC,0x00,0x3F,0xFE,0x01,0xFF,0xE0,0x01,0xFF,0xE0,0x0F,0xFF,0x00,0x0F,0xFF,0x00,0x3F,0xFC,0x00,0xFF,0xF0,0x01,0xFF,0xE0,0x07,0xFF,0x80,0x07,0xFF,0x80,0x7F,0xF8,0x00,0x3F,0xFC,0x03,0xFF,0xC0,0x00,0xFF,0xF0,0x3F,0xFC,0x00,0x07,0xFF,0x81,0xFF,0xE0,0x00,0x1F,0xFC,0x0F,0xFE,0x00,0x00,0xFF,0xF0,0xFF,0xF0,0x00,0x03,0xFF,0x87,0xFF,0x00,0x00,0x1F,0xFE,0x7F,0xF8,0x00,0x00,0x7F,0xF3,0xFF,0x80,0x00,0x03,0xFF,0xBF,0xFC,0x00,0x00,0x0F,0xFF,0xFF,0xC0,0x00,0x00,0x7F,0xFF,0xFE,0x00,0x00,0x01,0xFF,0xFF,0xE0,0x00,0x00,0x0F,0xFF,0xFF,0x00,0x00,0x00,0x3F,0xFF,0xF0,0x00,0x00,0x01,0xFF,0xFF,0x80,0x00,0x00,0x07,0xFF,0xF8,0x00,0x00,0x00,0x3F,0xFF,0xC0,0x00,0x00,0x00,0xFF,0xFC,0x00,0x00,0x00,0x07,0xFF,0xE0,0x00,0x00,0x00,0x1F,0xFE,0x00,0x00,0x00,0x00,0xFF,0xF0,0x00,0x00,0x00,0x07,0xFF,0x80,0x00,0x00,0x00,0x3F,0xFC,0x00,0x00,0x00,0x01,0xFF,0xE0,0x00,0x00,0x00,0x0F,0xFF,0x00,0x00,0x00,0x00,0x7F,0xF8,0x00,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0x00,0x1F,0xFE,0x00,0x00,0x00,0x00,0xFF,0xF0,0x00,0x00,0x00,0x07,0xFF,0x80,0x00,0x00,0x00,0x3F,0xFC,0x00,0x00,0x00,0x01,0xFF,0xE0,0x00,0x00,0x00,0x0F,0xFF,0x00,0x00,0x00,0x00,0x7F,0xF8,0x00,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0x00,0x1F,0xFE,0x00,0x00,0x00,0x00,0xFF,0xF0,0x00,0x00,0x00,0x07,0xFF,0x80,0x00,0x00, // 'Y'
	0xFF,0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,0xFF,0xFF,0xFC,0xFF,0xFF,0xFF,0xFF,0xFC,0xFF,0xFF,0xFF,0xFF,0xF8,0x00,0x00,0x00,0xFF,0xF0,0x00,0x00,0x01,0xFF,0xF0,0x00,0x00,0x03,0xFF,0xE0,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0x07,0xFF,0xC0,0x00,0x00,0x0F,0xFF,0x80,0x00,0x00,0x0F,0xFF,0x00,0x00,0x00,0x1F,0xFF,0x00,0x00,0x00,0x3F,0xFE,0x00,0x00,0x00,0x7F,0xFC,0x00,0x00,0x00,0x7F,0xFC,0x00,0x00,0x00,0xFF,0xF8,0x00,0x00,0x01,0xFF,0xF0,0x00,0x00,0x01,0xFF,0xF0,0x00,0x00,0x03,0xFF,0xE0,0x00,0x00,0x07,0xFF,0xC0,0x00,0x00,0x07,0xFF,0xC0,0x00,0x00,0x0F,0xFF,0x80,0x00,0x00,0x1F,0xFF,0x00,0x00,0x00,0x1F,0xFF,0x00,0x00,0x00,0x3F,0xFE,0x00,0x00,0x00,0x7F,0xFC,0x00,0x00,0x00,0x7F,0xFC,0x00,0x00,0x00,0xFF,0xF8,0x00,0x00,0x01,0xFF,0xF0,0x00,0x00,0x01,0xFF,0xF0,0x00,0x00,0x03,0xFF,0xE0,0x00,0x00,0x07,0xFF,0xC0,0x00,0x00,0x07,0xFF,0xC0,0x00,0x00,0x0F,0xFF,0x80,0x00,0x00,0x1F,0xFF,0x00,0x00,0x00,0x3F,0xFF,0x00,0x00,0x00,0x3F,0xFF,0xFF,0xFF,0xFE,0x7F,0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,0xFF,0xFF,0xFE, // 'Z'
	0xFF,0xFE,0xFF,0xFE,0xFF,0xFE,0xFF,0xFE,0xFF,0xFE,0xFF,0xFE,0xFF,0xFE,0xFF,0xFE,0xFF,0xFE,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xE0,0xFF,0xFE,0xFF,0xFE,0xFF,0xFE,0xFF,0xFE,0xFF,0xFE,0xFF,0xFE,0xFF,0xFE,0xFF,0xFE,0xFF,0xFE, // '['
	0xFF,0xF8,0x00,0x00,0x1F,0xFE,0x00,0x00,0x07,0xFF,0x80,0x00,0x00,0xFF,0xF0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x0F,0xFF,0x00,0x00,0x01,0xFF,0xE0,0x00,0x00,0x7F,0xF8,0x00,0x00,0x0F,0xFF,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0xFF,0xF0,0x00,0x00,0x1F,0xFE,0x00,0x00,0x07,0xFF,0x80,0x00,0x01,0xFF,0xF0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x0F,0xFF,0x00,0x00,0x01,0xFF,0xE0,0x00,0x00,0x7F,0xF8,0x00,0x00,0x1F,0xFE,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0xFF,0xF0,0x00,0x00,0x1F,0xFE,0x00,0x00,0x07,0xFF,0x80,0x00,0x01,0xFF,0xE0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x0F,0xFF,0x00,0x00,0x03,0xFF,0xE0,0x00,0x00,0x7F,0xF8,0x00,0x00,0x1F,0xFE,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0xFF,0xF0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x07,0xFF,0x80,0x00,0x01,0xFF,0xE0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x0F,0xFF,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0x7F,0xF8,0x00,0x00,0x1F,0xFE,0x00,0x00,0x07,0xFF,0xC0,0x00,0x00,0xFF,0xF0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x07,0xFF,0x80,0x00,0x01,0xFF,0xE0,0x00,0x00,0x7F,0xF8,0x00,0x00,0x0F,0xFF,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0x7F,0xF8,0x00,0x00,0x1F,0xFE,0x00,0x00,0x07,0xFF,0x80,0x00,0x00,0xFF,0xF0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x0F,0xFF,0x80,0x00,0x01,0xFF,0xE0, // '\'
	0xFF,0xFF,0x7F,0xFF,0xBF,0xFF,0xDF,0xFF,0xEF,0xFF,0xF7,0xFF,0xFB,0xFF,0xFD,0xFF,0xFE,0xFF,0xFF,0x03,0xFF,0x81,0xFF,0xC0,0xFF,0xE0,0x7F,0xF0,0x3F,0xF8,0x1F,0xFC,0x0F,0xFE,0x07,0xFF,0x03,0xFF,0x81,0xFF,0xC0,0xFF,0xE0,0x7F,0xF0,0x3F,0xF8,0x1F,0xFC,0x0F,0xFE,0x07,0xFF,0x03,0xFF,0x81,0xFF,0xC0,0xFF,0xE0,0x7F,0xF0,0x3F,0xF8,0x1F,0xFC,0x0F,0xFE,0x07,0xFF,0x03,0xFF,0x81,0xFF,0xC0,0xFF,0xE0,0x7F,0xF0,0x3F,0xF8,0x1F,0xFC,0x0F,0xFE,0x07,0xFF,0x03,0xFF,0x81,0xFF,0xC0,0xFF,0xE0,0x7F,0xF0,0x3F,0xF8,0x1F,0xFC,0x0F,0xFE,0x07,0xFF,0x03,0xFF,0x81,0xFF,0xC0,0xFF,0xE0,0x7F,0xF0,0x3F,0xF8,0x1F,0xFC,0x0F,0xFE,0x07,0xFF,0x03,0xFF,0x81,0xFF,0xC0,0xFF,0xE0,0x7F,0xF7,0xFF,0xFB,0xFF,0xFD,0xFF,0xFE,0xFF,0xFF,0x7F,0xFF,0xBF,0xFF,0xDF,0xFF,0xEF,0xFF,0xF7,0xFF,0xF8, // ']'
	0x00,0x3F,0xE0,0x00,0x00,0xFF,0x80,0x00,0x07,0xFF,0x00,0x00,0x1F,0xFC,0x00,0x00,0xFF,0xF8,0x00,0x03,0xFF,0xE0,0x00,0x0F,0xFF,0x80,0x00,0x7F,0xFF,0x00,0x01,0xFF,0xFC,0x00,0x0F,0xFF,0xF8,0x00,0x3F,0xDF,0xE0,0x00,0xFF,0x7F,0xC0,0x07,0xF8,0xFF,0x00,0x1F,0xE3,0xFC,0x00,0xFF,0x8F,0xF8,0x03,0xFC,0x1F,0xE0,0x0F,0xF0,0x7F,0xC0,0x7F,0xC0,0xFF,0x01,0xFE,0x03,0xFC,0x0F,0xF8,0x0F,0xF8,0x3F,0xC0,0x1F,0xE1,0xFF,0x00,0x7F,0xC7,0xFC,0x01,0xFF,0x1F,0xE0,0x03,0xFC,0xFF,0x80,0x0F,0xF8, // '^'
	0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,0xFF,0xFE, // '_'
	0xFF,0xF0,0x07,0xFF,0x80,0x3F,0xFC,0x01,0xFF,0xC0,0x0F,0xFE,0x00,0x7F,0xF0,0x03,0xFF,0x00,0x3F,0xF8,0x01,0xFF,0xC0,0x0F,0xFC,0x00,0x7F,0xE0, // '`'
	0x00,0x0F,0xFC,0x00,0x00,0x1F,0xFF,0xF0,0x00,0x0F,0xFF,0xFF,0x00,0x03,0xFF,0xFF,0xF8,0x00,0xFF,0xFF,0xFF,0x80,0x3F,0xFF,0xFF,0xF8,0x0F,0xFF,0xFF,0xFF,0x03,0xFF,0xFF,0xFF,0xF0,0x7F,0xFC,0x3F,0xFE,0x0F,0xFF,0x03,0xFF,0xE1,0xFF,0xC0,0x3F,0xFC,0x7F,0xF8,0x07,0xFF,0x80,0x00,0x00,0xFF,0xF0,0x00,0x00,0x1F,0xFE,0x00,0x00,0x03,0xFF,0xC0,0x00,0xFF,0xFF,0xF8,0x01,0xFF,0xFF,0xFF,0x00,0xFF,0xFF,0xFF,0xE0,0x3F,0xFF,0xFF,0xFC,0x1F,0xFF,0xFF,0xFF,0x83,0xFF,0xFF,0xFF,0xF0,0xFF,0xFF,0xFF,0xFE,0x3F,0xFF,0x03,0xFF,0xC7,0xFF,0x80,0x7F,0xF8,0xFF,0xF0,0x0F,0xFF,0x1F,0xFC,0x01,0xFF,0xE3,0xFF,0x80,0x3F,0xFC,0x7F,0xF0,0x07,0xFF,0x8F,0xFE,0x00,0xFF,0xF1,0xFF,0xE0,0x3F,0xFE,0x3F,0xFE,0x0F,0xFF,0xC7,0xFF,0xFF,0xFF,0xF8,0x7F,0xFF,0xFF,0xFF,0x0F,0xFF,0xFF,0xFF,0xE0,0xFF,0xFF,0xFF,0xFE,0x0F,0xFF,0xFF,0xFF,0xC0,0xFF,0xFF,0x7F,0xF8,0x07,0xFF,0x87,0xFF,0x80,0x3F,0xC0,0x00,0x00, // 'a'
	0xFF,0xF0,0x00,0x00,0x1F,0xFE,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0x7F,0xF8,0x00,0x00,0x0F,0xFF,0x00,0x00,0x01,0xFF,0xE0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x07,0xFF,0x80,0x00,0x00,0xFF,0xF0,0x00,0x00,0x1F,0xFE,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0x7F,0xF8,0x00,0x00,0x0F,0xFF,0x00,0x00,0x01,0xFF,0xE0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x07,0xFF,0x87,0xF8,0x00,0xFF,0xF3,0xFF,0xC0,0x1F,0xFE,0xFF,0xFE,0x03,0xFF,0xFF,0xFF,0xE0,0x7F,0xFF,0xFF,0xFE,0x0F,0xFF,0xFF,0xFF,0xE1,0xFF,0xFF,0xFF,0xFE,0x3F,0xFF,0xFF,0xFF,0xC7,0xFF,0xFF,0xFF,0xFC,0xFF,0xFC,0x1F,0xFF,0x9F,0xFF,0x00,0xFF,0xF3,0xFF,0xC0,0x1F,0xFE,0x7F,0xF8,0x01,0xFF,0xEF,0xFF,0x00,0x3F,0xFD,0xFF,0xE0,0x07,0xFF,0xBF,0xFC,0x00,0x7F,0xF7,0xFF,0x80,0x0F,0xFE,0xFF,0xF0,0x01,0xFF,0xDF,0xFE,0x00,0x3F,0xFB,0xFF,0xC0,0x07,0xFF,0x7F,0xF8,0x00,0xFF,0xEF,0xFF,0x00,0x1F,0xFD,0xFF,0xE0,0x03,0xFF,0xBF,0xFC,0x00,0x7F,0xF7,0xFF,0x80,0x1F,0xFE,0xFF,0xF0,0x03,0xFF,0xDF,0xFE,0x00,0x7F,0xFB,0xFF,0xC0,0x0F,0xFF,0x7F,0xFC,0x03,0xFF,0xCF,0xFF,0xC0,0xFF,0xF9,0xFF,0xFF,0xFF,0xFF,0x3F,0xFF,0xFF,0xFF,0xC7,0xFF,0xFF,0xFF,0xF8,0xFF,0xFF,0xFF,0xFE,0x1F,0xFF,0xFF,0xFF,0x83,0xFF,0xBF,0xFF,0xE0,0x7F,0xF3,0xFF,0xF8,0x0F,0xFE,0x3F,0xFE,0x00,0x00,0x00,0xFE,0x00,0x00, // 'b'
	0x00,0x0F,0xF8,0x00,0x00,0x1F,0xFF,0xC0,0x00,0x1F,0xFF,0xFC,0x00,0x0F,0xFF,0xFF,0xC0,0x07,0xFF,0xFF,0xF8,0x03,0xFF,0xFF,0xFF,0x01,0xFF,0xFF,0xFF,0xC0,0xFF,0xFF,0xFF,0xF8,0x3F,0xFF,0xFF,0xFE,0x1F,0xFF,0x07,0xFF,0xC7,0xFF,0x80,0xFF,0xF1,0xFF,0xC0,0x1F,0xFC,0xFF,0xF0,0x07,0xFF,0x3F,0xFC,0x00,0xFF,0xCF,0xFE,0x00,0x3F,0xFB,0xFF,0x80,0x00,0x00,0xFF,0xE0,0x00,0x00,0x3F,0xF8,0x00,0x00,0x0F,0xFE,0x00,0x00,0x03,0xFF,0x80,0x00,0x00,0xFF,0xE0,0x00,0x00,0x3F,0xF8,0x00,0x00,0x0F,0xFE,0x00,0x00,0x03,0xFF,0x80,0x00,0x00,0xFF,0xE0,0x00,0x00,0x3F,0xFC,0x00,0xFF,0xEF,0xFF,0x00,0x3F,0xF1,0xFF,0xC0,0x1F,0xFC,0x7F,0xF8,0x07,0xFF,0x1F,0xFF,0x07,0xFF,0xC3,0xFF,0xFF,0xFF,0xE0,0xFF,0xFF,0xFF,0xF8,0x1F,0xFF,0xFF,0xFC,0x03,0xFF,0xFF,0xFF,0x00,0x7F,0xFF,0xFF,0x80,0x0F,0xFF,0xFF,0xC0,0x01,0xFF,0xFF,0xC0,0x00,0x1F,0xFF,0xE0,0x00,0x00,0xFF,0xC0,0x00, // 'c'
	0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0x7F,0xF8,0x00,0x00,0x0F,0xFF,0x00,0x00,0x01,0xFF,0xE0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x07,0xFF,0x80,0x00,0x00,0xFF,0xF0,0x00,0x00,0x1F,0xFE,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0x7F,0xF8,0x00,0x00,0x0F,0xFF,0x00,0x00,0x01,0xFF,0xE0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x07,0xFF,0x80,0x00,0x00,0xFF,0xF0,0x01,0xFC,0x1F,0xFE,0x00,0xFF,0xE3,0xFF,0xC0,0x7F,0xFF,0x7F,0xF8,0x1F,0xFF,0xFF,0xFF,0x07,0xFF,0xFF,0xFF,0xE1,0xFF,0xFF,0xFF,0xFC,0x7F,0xFF,0xFF,0xFF,0x8F,0xFF,0xFF,0xFF,0xF3,0xFF,0xFF,0xFF,0xFE,0x7F,0xFE,0x1F,0xFF,0xCF,0xFF,0x00,0xFF,0xFB,0xFF,0xE0,0x0F,0xFF,0x7F,0xF8,0x01,0xFF,0xEF,0xFF,0x00,0x3F,0xFD,0xFF,0xE0,0x07,0xFF,0xBF,0xF8,0x00,0xFF,0xF7,0xFF,0x00,0x1F,0xFE,0xFF,0xE0,0x03,0xFF,0xDF,0xFC,0x00,0x7F,0xFB,0xFF,0x80,0x0F,0xFF,0x7F,0xF0,0x01,0xFF,0xEF,0xFE,0x00,0x3F,0xFD,0xFF,0xC0,0x07,0xFF,0xBF,0xF8,0x00,0xFF,0xF7,0xFF,0x00,0x1F,0xFE,0xFF,0xF0,0x03,0xFF,0xDF,0xFE,0x00,0x7F,0xF9,0xFF,0xE0,0x0F,0xFF,0x3F,0xFC,0x03,0xFF,0xE7,0xFF,0xE1,0xFF,0xFC,0x7F,0xFF,0xFF,0xFF,0x8F,0xFF,0xFF,0xFF,0xF0,0xFF,0xFF,0xFF,0xFE,0x1F,0xFF,0xFF,0xFF,0xC1,0xFF,0xFF,0xFF,0xF8,0x1F,0xFF,0xF7,0xFF,0x01,0xFF,0xFC,0xFF,0xE0,0x0F,0xFE,0x1F,0xFC,0x00,0x7F,0x00,0x00,0x00, // 'd'
	0x00,0x07,0xFC,0x00,0x00,0x03,0xFF,0xF8,0x00,0x00,0xFF,0xFF,0xE0,0x00,0x1F,0xFF,0xFF,0x80,0x03,0xFF,0xFF,0xFC,0x00,0x7F,0xFF,0xFF,0xE0,0x0F,0xFF,0xFF,0xFE,0x01,0xFF,0xFF,0xFF,0xF0,0x1F,0xFF,0xFF,0xFF,0x83,0xFF,0xE0,0xFF,0xF8,0x3F,0xFC,0x03,0xFF,0x87,0xFF,0x80,0x1F,0xFC,0x7F,0xF8,0x01,0xFF,0xC7,0xFF,0x00,0x1F,0xFC,0xFF,0xF0,0x01,0xFF,0xCF,0xFF,0x00,0x1F,0xFE,0xFF,0xFF,0xFF,0xFF,0xEF,0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,0xFF,0xFF,0xEF,0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,0xFF,0xFF,0xEF,0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,0xFF,0xFF,0xEF,0xFF,0x00,0x00,0x00,0xFF,0xF0,0x00,0x00,0x07,0xFF,0x80,0x00,0x00,0x7F,0xF8,0x00,0x08,0x07,0xFF,0xC0,0x01,0xC0,0x3F,0xFE,0x00,0x3E,0x03,0xFF,0xF8,0x1F,0xE0,0x1F,0xFF,0xFF,0xFF,0x01,0xFF,0xFF,0xFF,0xF8,0x0F,0xFF,0xFF,0xFF,0xC0,0x7F,0xFF,0xFF,0xF8,0x03,0xFF,0xFF,0xFF,0x00,0x1F,0xFF,0xFF,0xE0,0x00,0x7F,0xFF,0xF8,0x00,0x01,0xFF,0xFE,0x00,0x00,0x03,0xFF,0x00,0x00, // 'e'
	0x00,0x01,0xFF,0x00,0x03,0xFF,0xE0,0x03,0xFF,0xF8,0x01,0xFF,0xFE,0x00,0xFF,0xFF,0x80,0x3F,0xFF,0xE0,0x1F,0xFF,0xF8,0x07,0xFF,0xFE,0x03,0xFF,0xFF,0x80,0xFF,0xFC,0x00,0x3F,0xFC,0x00,0x0F,0xFE,0x00,0x03,0xFF,0x80,0x00,0xFF,0xE0,0x00,0x3F,0xF8,0x00,0x0F,0xFE,0x00,0xFF,0xFF,0xFF,0x3F,0xFF,0xFF,0xCF,0xFF,0xFF,0xF3,0xFF,0xFF,0xFC,0xFF,0xFF,0xFF,0x3F,0xFF,0xFF,0xCF,0xFF,0xFF,0xF3,0xFF,0xFF,0xFC,0x03,0xFF,0x80,0x00,0xFF,0xE0,0x00,0x3F,0xF8,0x00,0x0F,0xFE,0x00,0x03,0xFF,0x80,0x00,0xFF,0xE0,0x00,0x3F,0xF8,0x00,0x0F,0xFE,0x00,0x03,0xFF,0x80,0x00,0xFF,0xE0,0x00,0x3F,0xF8,0x00,0x0F,0xFE,0x00,0x03,0xFF,0x80,0x00,0xFF,0xE0,0x00,0x3F,0xF8,0x00,0x0F,0xFE,0x00,0x03,0xFF,0x80,0x00,0xFF,0xE0,0x00,0x3F,0xF8,0x00,0x0F,0xFE,0x00,0x03,0xFF,0x80,0x00,0xFF,0xE0,0x00,0x3F,0xF8,0x00,0x0F,0xFE,0x00,0x03,0xFF,0x80,0x00,0xFF,0xE0,0x00,0x3F,0xF8,0x00,0x0F,0xFE,0x00,0x03,0xFF,0x80,0x00, // 'f'
	0x00,0x1F,0xC0,0x00,0x00,0x0F,0xFF,0x8F,0xFE,0x01,0xFF,0xFC,0xFF,0xE0,0x7F,0xFF,0xEF,0xFE,0x0F,0xFF,0xFF,0xFF,0xE0,0xFF,0xFF,0xFF,0xFE,0x1F,0xFF,0xFF,0xFF,0xE3,0xFF,0xFF,0xFF,0xFE,0x3F,0xFF,0xFF,0xFF,0xE7,0xFF,0xF0,0x7F,0xFE,0x7F,0xFC,0x03,0xFF,0xE7,0xFF,0x80,0x1F,0xFE,0xFF,0xF8,0x01,0xFF,0xEF,0xFF,0x00,0x1F,0xFE,0xFF,0xF0,0x01,0xFF,0xEF,0xFF,0x00,0x1F,0xFE,0xFF,0xE0,0x01,0xFF,0xEF,0xFE,0x00,0x1F,0xFE,0xFF,0xE0,0x01,0xFF,0xEF,0xFE,0x00,0x1F,0xFE,0xFF,0xE0,0x01,0xFF,0xEF,0xFE,0x00,0x1F,0xFE,0xFF,0xE0,0x01,0xFF,0xEF,0xFF,0x00,0x1F,0xFE,0xFF,0xF0,0x01,0xFF,0xEF,0xFF,0x00,0x1F,0xFE,0xFF,0xF8,0x01,0xFF,0xE7,0xFF,0x80,0x1F,0xFE,0x7F,0xFC,0x03,0xFF,0xE7,0xFF,0xE0,0x7F,0xFE,0x3F,0xFF,0xFF,0xFF,0xE3,0xFF,0xFF,0xFF,0xFE,0x1F,0xFF,0xFF,0xFF,0xE0,0xFF,0xFF,0xFF,0xFE,0x07,0xFF,0xFF,0xFF,0xE0,0x7F,0xFF,0xFF,0xFE,0x01,0xFF,0xFD,0xFF,0xE0,0x0F,0xFF,0x9F,0xFE,0x00,0x1F,0xC1,0xFF,0xE0,0x00,0x00,0x1F,0xFE,0x02,0x00,0x03,0xFF,0xC0,0x30,0x00,0x3F,0xFC,0x03,0xC0,0x07,0xFF,0xC0,0x7F,0x01,0xFF,0xFC,0x0F,0xFF,0xFF,0xFF,0x80,0xFF,0xFF,0xFF,0xF8,0x1F,0xFF,0xFF,0xFF,0x03,0xFF,0xFF,0xFF,0xE0,0x1F,0xFF,0xFF,0xFC,0x00,0x7F,0xFF,0xFF,0x80,0x03,0xFF,0xFF,0xE0,0x00,0x0F,0xFF,0xF8,0x00,0x00,0x0F,0xF8,0x00,0x00, // 'g'
	0xFF,0xF0,0x00,0x00,0x1F,0xFE,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0x7F,0xF8,0x00,0x00,0x0F,0xFF,0x00,0x00,0x01,0xFF,0xE0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x07,0xFF,0x80,0x00,0x00,0xFF,0xF0,0x00,0x00,0x1F,0xFE,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0x7F,0xF8,0x00,0x00,0x0F,0xFF,0x00,0x00,0x01,0xFF,0xE0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x07,0xFF,0x81,0xFC,0x00,0xFF,0xF1,0xFF,0xF0,0x1F,0xFE,0x7F,0xFF,0x03,0xFF,0xDF,0xFF,0xF0,0x7F,0xFF,0xFF,0xFF,0x0F,0xFF,0xFF,0xFF,0xF1,0xFF,0xFF,0xFF,0xFE,0x3F,0xFF,0xFF,0xFF,0xC7,0xFF,0xFF,0xFF,0xFC,0xFF,0xFC,0x1F,0xFF,0x9F,0xFE,0x00,0xFF,0xF3,0xFF,0xC0,0x1F,0xFE,0x7F,0xF8,0x01,0xFF,0xCF,0xFF,0x00,0x3F,0xFD,0xFF,0xE0,0x07,0xFF,0xBF,0xFC,0x00,0xFF,0xF7,0xFF,0x80,0x1F,0xFE,0xFF,0xF0,0x03,0xFF,0xDF,0xFE,0x00,0x7F,0xFB,0xFF,0xC0,0x0F,0xFF,0x7F,0xF8,0x01,0xFF,0xEF,0xFF,0x00,0x3F,0xFD,0xFF,0xE0,0x07,0xFF,0xBF,0xFC,0x00,0xFF,0xF7,0xFF,0x80,0x1F,0xFE,0xFF,0xF0,0x03,0xFF,0xDF,0xFE,0x00,0x7F,0xFB,0xFF,0xC0,0x0F,0xFF,0x7F,0xF8,0x01,0xFF,0xEF,0xFF,0x00,0x3F,0xFD,0xFF,0xE0,0x07,0xFF,0xBF,0xFC,0x00,0xFF,0xF7,0xFF,0x80,0x1F,0xFE,0xFF,0xF0,0x03,0xFF,0xDF,0xFE,0x00,0x7F,0xFB,0xFF,0xC0,0x0F,0xFF,0x7F,0xF8,0x01,0xFF,0xEF,0xFF,0x00,0x3F,0xFC, // 'h'
	0x0F,0x80,0xFF,0x87,0xFF,0x3F,0xFC,0xFF,0xFB,0xFF,0xEF,0xFF,0xBF,0xFC,0x7F,0xF0,0xFF,0x80,0xF8,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1F,0xFC,0x7F,0xF1,0xFF,0xC7,0xFF,0x1F,0xFC,0x7F,0xF1,0xFF,0xC7,0xFF,0x1F,0xFC,0x7F,0xF1,0xFF,0xC7,0xFF,0x1F,0xFC,0x7F,0xF1,0xFF,0xC7,0xFF,0x1F,0xFC,0x7F,0xF1,0xFF,0xC7,0xFF,0x1F,0xFC,0x7F,0xF1,0xFF,0xC7,0xFF,0x1F,0xFC,0x7F,0xF1,0xFF,0xC7,0xFF,0x1F,0xFC,0x7F,0xF1,0xFF,0xC7,0xFF,0x1F,0xFC,0x7F,0xF1,0xFF,0xC7,0xFF,0x1F,0xFC, // 'i'
	0x00,0x1F,0x00,0x03,0xFE,0x00,0x3F,0xF8,0x01,0xFF,0xE0,0x1F,0xFF,0x00,0xFF,0xF8,0x07,0xFF,0xC0,0x1F,0xFE,0x00,0xFF,0xE0,0x03,0xFE,0x00,0x07,0xE0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1F,0xFE,0x00,0xFF,0xF0,0x07,0xFF,0x80,0x3F,0xFC,0x01,0xFF,0xE0,0x0F,0xFF,0x00,0x7F,0xF8,0x03,0xFF,0xC0,0x1F,0xFE,0x00,0xFF,0xF0,0x07,0xFF,0x80,0x3F,0xFC,0x01,0xFF,0xE0,0x0F,0xFF,0x00,0x7F,0xF8,0x03,0xFF,0xC0,0x1F,0xFE,0x00,0xFF,0xF0,0x07,0xFF,0x80,0x3F,0xFC,0x01,0xFF,0xE0,0x0F,0xFF,0x00,0x7F,0xF8,0x03,0xFF,0xC0,0x1F,0xFE,0x00,0xFF,0xF0,0x07,0xFF,0x80,0x3F,0xFC,0x01,0xFF,0xE0,0x0F,0xFF,0x00,0x7F,0xF8,0x03,0xFF,0xC0,0x1F,0xFE,0x00,0xFF,0xF0,0x07,0xFF,0x80,0x3F,0xFC,0x01,0xFF,0xE0,0x0F,0xFF,0x00,0x7F,0xF8,0x03,0xFF,0xC0,0x1F,0xFE,0x01,0xFF,0xE0,0x1F,0xFF,0x3F,0xFF,0xF9,0xFF,0xFF,0xCF,0xFF,0xFC,0x7F,0xFF,0xE3,0xFF,0xFE,0x1F,0xFF,0xE0,0xFF,0xFE,0x07,0xFF,0xC0,0x1F,0xF0,0x00, // 'j'
	0xFF,0xF0,0x00,0x00,0x07,0xFF,0x80,0x00,0x00,0x3F,0xFC,0x00,0x00,0x01,0xFF,0xE0,0x00,0x00,0x0F,0xFF,0x00,0x00,0x00,0x7F,0xF8,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0x1F,0xFE,0x00,0x00,0x00,0xFF,0xF0,0x00,0x00,0x07,0xFF,0x80,0x00,0x00,0x3F,0xFC,0x00,0x00,0x01,0xFF,0xE0,0x00,0x00,0x0F,0xFF,0x00,0x00,0x00,0x7F,0xF8,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0x1F,0xFE,0x00,0x00,0x00,0xFF,0xF0,0x07,0xFF,0xE7,0xFF,0x80,0x7F,0xFE,0x3F,0xFC,0x03,0xFF,0xE1,0xFF,0xE0,0x3F,0xFF,0x0F,0xFF,0x03,0xFF,0xF0,0x7F,0xF8,0x3F,0xFF,0x03,0xFF,0xC3,0xFF,0xF0,0x1F,0xFE,0x1F,0xFF,0x00,0xFF,0xF1,0xFF,0xF0,0x07,0xFF,0x9F,0xFF,0x00,0x3F,0xFD,0xFF,0xF8,0x01,0xFF,0xFF,0xFF,0x80,0x0F,0xFF,0xFF,0xF8,0x00,0x7F,0xFF,0xFF,0x80,0x03,0xFF,0xFF,0xF8,0x00,0x1F,0xFF,0xFF,0xC0,0x00,0xFF,0xFF,0xFE,0x00,0x07,0xFF,0xFF,0xF8,0x00,0x3F,0xFF,0xFF,0xE0,0x01,0xFF,0xFF,0xFF,0x00,0x0F,0xFF,0xFF,0xFC,0x00,0x7F,0xFF,0xFF,0xF0,0x03,0xFF,0xFF,0xFF,0x80,0x1F,0xFF,0xFF,0xFE,0x00,0xFF,0xFC,0xFF,0xF0,0x07,0xFF,0xC7,0xFF,0xC0,0x3F,0xFC,0x1F,0xFF,0x01,0xFF,0xE0,0x7F,0xF8,0x0F,0xFF,0x03,0xFF,0xE0,0x7F,0xF8,0x0F,0xFF,0x83,0xFF,0xC0,0x7F,0xFC,0x1F,0xFE,0x01,0xFF,0xF0,0xFF,0xF0,0x07,0xFF,0x87,0xFF,0x80,0x3F,0xFE,0x3F,0xFC,0x00,0xFF,0xF9,0xFF,0xE0,0x07,0xFF,0xCF,0xFF,0x00,0x1F,0xFF,0x00, // 'k'
	0xFF,0xEF,0xFE,0xFF,0xEF,0xFE,0xFF,0xEF,0xFE,0xFF,0xEF,0xFE,0xFF,0xEF,0xFE,0xFF,0xEF,0xFE,0xFF,0xEF,0xFE,0xFF,0xEF,0xFE,0xFF,0xEF,0xFE,0xFF,0xEF,0xFE,0xFF,0xEF,0xFE,0xFF,0xEF,0xFE,0xFF,0xEF,0xFE,0xFF,0xEF,0xFE,0xFF,0xEF,0xFE,0xFF,0xEF,0xFE,0xFF,0xEF,0xFE,0xFF,0xEF,0xFE,0xFF,0xEF,0xFE,0xFF,0xEF,0xFE,0xFF,0xEF,0xFE,0xFF,0xEF,0xFE,0xFF,0xEF,0xFE,0xFF,0xEF,0xFE,0xFF,0xEF,0xFE,0xFF,0xEF,0xFE,0xFF,0xE0, // 'l'
	0x00,0x00,0x3F,0xC0,0x03,0xFC,0x01,0xFF,0xC3,0xFF,0xE0,0x1F,0xFE,0x03,0xFF,0x8F,0xFF,0xE0,0xFF,0xFF,0x07,0xFF,0x3F,0xFF,0xE3,0xFF,0xFF,0x0F,0xFE,0xFF,0xFF,0xEF,0xFF,0xFE,0x1F,0xFF,0xFF,0xFF,0xFF,0xFF,0xFE,0x3F,0xFF,0xFF,0xFF,0xFF,0xFF,0xFC,0x7F,0xFF,0xFF,0xFF,0xFF,0xFF,0xFC,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF9,0xFF,0xF8,0x3F,0xFF,0xC1,0xFF,0xF3,0xFF,0xE0,0x3F,0xFF,0x01,0xFF,0xF7,0xFF,0x80,0x3F,0xFC,0x01,0xFF,0xEF,0xFF,0x00,0x7F,0xF8,0x03,0xFF,0xDF,0xFE,0x00,0xFF,0xF0,0x07,0xFF,0xBF,0xFC,0x01,0xFF,0xE0,0x0F,0xFF,0x7F,0xF8,0x03,0xFF,0xC0,0x1F,0xFE,0xFF,0xF0,0x07,0xFF,0x80,0x3F,0xFD,0xFF,0xE0,0x0F,0xFF,0x00,0x7F,0xFB,0xFF,0xC0,0x1F,0xFE,0x00,0xFF,0xF7,0xFF,0x80,0x3F,0xFC,0x01,0xFF,0xEF,0xFF,0x00,0x7F,0xF8,0x03,0xFF,0xDF,0xFE,0x00,0xFF,0xF0,0x07,0xFF,0xBF,0xFC,0x01,0xFF,0xE0,0x0F,0xFF,0x7F,0xF8,0x03,0xFF,0xC0,0x1F,0xFE,0xFF,0xF0,0x07,0xFF,0x80,0x3F,0xFD,0xFF,0xE0,0x0F,0xFF,0x00,0x7F,0xFB,0xFF,0xC0,0x1F,0xFE,0x00,0xFF,0xF7,0xFF,0x80,0x3F,0xFC,0x01,0xFF,0xEF,0xFF,0x00,0x7F,0xF8,0x03,0xFF,0xDF,0xFE,0x00,0xFF,0xF0,0x07,0xFF,0xBF,0xFC,0x01,0xFF,0xE0,0x0F,0xFF,0x7F,0xF8,0x03,0xFF,0xC0,0x1F,0xFE,0xFF,0xF0,0x07,0xFF,0x80,0x3F,0xFD,0xFF,0xE0,0x0F,0xFF,0x00,0x7F,0xFB,0xFF,0xC0,0x1F,0xFE,0x00,0xFF,0xF7,0xFF,0x80,0x3F,0xFC,0x01,0xFF,0xEF,0xFF,0x00,0x7F,0xF8,0x03,0xFF,0xDF,0xFE,0x00,0xFF,0xF0,0x07,0xFF,0x80, // 'm'
	0x00,0x00,0x3F,0x80,0x3F,0xF8,0x7F,0xFC,0x0F,0xFE,0x3F,0xFF,0x83,0xFF,0x9F,0xFF,0xF0,0xFF,0xEF,0xFF,0xFE,0x3F,0xFF,0xFF,0xFF,0x8F,0xFF,0xFF,0xFF,0xF3,0xFF,0xFF,0xFF,0xFC,0xFF,0xFF,0xFF,0xFF,0x3F,0xFF,0x07,0xFF,0xEF,0xFF,0x00,0x7F,0xFB,0xFF,0x80,0x1F,0xFE,0xFF,0xE0,0x07,0xFF,0xBF,0xF8,0x01,0xFF,0xEF,0xFE,0x00,0x3F,0xFB,0xFF,0x80,0x0F,0xFE,0xFF,0xE0,0x03,0xFF,0xBF,0xF8,0x00,0xFF,0xEF,0xFE,0x00,0x3F,0xFB,0xFF,0x80,0x0F,0xFE,0xFF,0xE0,0x03,0xFF,0xBF,0xF8,0x00,0xFF,0xEF,0xFE,0x00,0x3F,0xFB,0xFF,0x80,0x0F,0xFE,0xFF,0xE0,0x03,0xFF,0xBF,0xF8,0x00,0xFF,0xEF,0xFE,0x00,0x3F,0xFB,0xFF,0x80,0x0F,0xFE,0xFF,0xE0,0x03,0xFF,0xBF,0xF8,0x00,0xFF,0xEF,0xFE,0x00,0x3F,0xFB,0xFF,0x80,0x0F,0xFE,0xFF,0xE0,0x03,0xFF,0xBF,0xF8,0x00,0xFF,0xEF,0xFE,0x00,0x3F,0xFB,0xFF,0x80,0x0F,0xFE,0xFF,0xE0,0x03,0xFF,0xBF,0xF8,0x00,0xFF,0xE0, // 'n'
	0x00,0x07,0xFC,0x00,0x00,0x03,0xFF,0xF8,0x00,0x00,0xFF,0xFF,0xE0,0x00,0x3F,0xFF,0xFF,0x80,0x07,0xFF,0xFF,0xFC,0x00,0xFF,0xFF,0xFF,0xE0,0x1F,0xFF,0xFF,0xFF,0x01,0xFF,0xFF,0xFF,0xF0,0x3F,0xFF,0xFF,0xFF,0x83,0xFF,0xE0,0xFF,0xF8,0x7F,0xFC,0x07,0xFF,0xC7,0xFF,0x80,0x3F,0xFC,0x7F,0xF0,0x01,0xFF,0xEF,0xFF,0x00,0x1F,0xFE,0xFF,0xF0,0x01,0xFF,0xEF,0xFF,0x00,0x0F,0xFE,0xFF,0xE0,0x00,0xFF,0xEF,0xFE,0x00,0x0F,0xFE,0xFF,0xE0,0x00,0xFF,0xEF,0xFE,0x00,0x0F,0xFE,0xFF,0xE0,0x00,0xFF,0xEF,0xFE,0x00,0x0F,0xFE,0xFF,0xE0,0x00,0xFF,0xEF,0xFF,0x00,0x0F,0xFE,0xFF,0xF0,0x01,0xFF,0xEF,0xFF,0x00,0x1F,0xFE,0x7F,0xF0,0x01,0xFF,0xE7,0xFF,0x80,0x3F,0xFC,0x7F,0xFC,0x03,0xFF,0xC3,0xFF,0xE0,0xFF,0xFC,0x3F,0xFF,0xFF,0xFF,0x81,0xFF,0xFF,0xFF,0xF0,0x1F,0xFF,0xFF,0xFF,0x00,0xFF,0xFF,0xFF,0xE0,0x07,0xFF,0xFF,0xFC,0x00,0x3F,0xFF,0xFF,0x80,0x00,0xFF,0xFF,0xE0,0x00,0x03,0xFF,0xFC,0x00,0x00,0x07,0xFC,0x00,0x00, // 'o'
	0x00,0x00,0x7F,0x00,0x1F,0xFC,0x7F,0xF8,0x03,0xFF,0x9F,0xFF,0xC0,0x7F,0xF7,0xFF,0xFC,0x0F,0xFF,0xFF,0xFF,0xC1,0xFF,0xFF,0xFF,0xFC,0x3F,0xFF,0xFF,0xFF,0x87,0xFF,0xFF,0xFF,0xF8,0xFF,0xFF,0xFF,0xFF,0x1F,0xFF,0x83,0xFF,0xF3,0xFF,0xE0,0x1F,0xFE,0x7F,0xF8,0x03,0xFF,0xCF,0xFF,0x00,0x3F,0xFD,0xFF,0xE0,0x07,0xFF,0xBF,0xFC,0x00,0xFF,0xF7,0xFF,0x80,0x0F,0xFE,0xFF,0xF0,0x01,0xFF,0xDF,0xFE,0x00,0x3F,0xFB,0xFF,0xC0,0x07,0xFF,0x7F,0xF8,0x00,0xFF,0xEF,0xFF,0x00,0x1F,0xFD,0xFF,0xE0,0x03,0xFF,0xBF,0xFC,0x00,0x7F,0xF7,0xFF,0x80,0x0F,0xFE,0xFF,0xF0,0x03,0xFF,0xDF,0xFE,0x00,0x7F,0xFB,0xFF,0xC0,0x0F,0xFF,0x7F,0xF8,0x03,0xFF,0xCF,0xFF,0x80,0x7F,0xF9,0xFF,0xF8,0x3F,0xFF,0x3F,0xFF,0xFF,0xFF,0xC7,0xFF,0xFF,0xFF,0xF8,0xFF,0xFF,0xFF,0xFE,0x1F,0xFF,0xFF,0xFF,0xC3,0xFF,0xFF,0xFF,0xF0,0x7F,0xFF,0xFF,0xFC,0x0F,0xFF,0x7F,0xFF,0x01,0xFF,0xE7,0xFF,0x80,0x3F,0xFC,0x3F,0xC0,0x07,0xFF,0x80,0x00,0x00,0xFF,0xF0,0x00,0x00,0x1F,0xFE,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0x7F,0xF8,0x00,0x00,0x0F,0xFF,0x00,0x00,0x01,0xFF,0xE0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x07,0xFF,0x80,0x00,0x00,0xFF,0xF0,0x00,0x00,0x1F,0xFE,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0x7F,0xF8,0x00,0x00,0x00, // 'p'
	0x00,0x3F,0x80,0x00,0x00,0x1F,0xFC,0x1F,0xF8,0x0F,0xFF,0xE3,0xFF,0x03,0xFF,0xFE,0xFF,0xE0,0xFF,0xFF,0xFF,0xFC,0x3F,0xFF,0xFF,0xFF,0x8F,0xFF,0xFF,0xFF,0xF1,0xFF,0xFF,0xFF,0xFE,0x7F,0xFF,0xFF,0xFF,0xCF,0xFF,0xC1,0xFF,0xF9,0xFF,0xE0,0x1F,0xFF,0x7F,0xFC,0x01,0xFF,0xEF,0xFF,0x00,0x3F,0xFD,0xFF,0xE0,0x07,0xFF,0xBF,0xFC,0x00,0xFF,0xF7,0xFF,0x00,0x1F,0xFE,0xFF,0xE0,0x03,0xFF,0xDF,0xFC,0x00,0x7F,0xFB,0xFF,0x80,0x0F,0xFF,0x7F,0xF0,0x01,0xFF,0xEF,0xFE,0x00,0x3F,0xFD,0xFF,0xC0,0x07,0xFF,0xBF,0xF8,0x00,0xFF,0xF7,0xFF,0x00,0x1F,0xFE,0xFF,0xF0,0x03,0xFF,0xDF,0xFE,0x00,0x7F,0xFB,0xFF,0xC0,0x0F,0xFF,0x3F,0xFC,0x01,0xFF,0xE7,0xFF,0x80,0x7F,0xFC,0xFF,0xFC,0x1F,0xFF,0x8F,0xFF,0xFF,0xFF,0xF1,0xFF,0xFF,0xFF,0xFE,0x1F,0xFF,0xFF,0xFF,0xC3,0xFF,0xFF,0xFF,0xF8,0x3F,0xFF,0xFF,0xFF,0x03,0xFF,0xFF,0xFF,0xE0,0x3F,0xFF,0xBF,0xFC,0x01,0xFF,0xE7,0xFF,0x80,0x0F,0xE0,0xFF,0xF0,0x00,0x00,0x1F,0xFE,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0x7F,0xF8,0x00,0x00,0x0F,0xFF,0x00,0x00,0x01,0xFF,0xE0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x07,0xFF,0x80,0x00,0x00,0xFF,0xF0,0x00,0x00,0x1F,0xFE,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0x7F,0xF8,0x00,0x00,0x0F,0xFF,0x00,0x00,0x01,0xFF,0xE0, // 'q'
	0x00,0x00,0xFD,0xFF,0x87,0xFB,0xFF,0x9F,0xF7,0xFF,0x7F,0xEF,0xFF,0xFF,0xDF,0xFF,0xFF,0xBF,0xFF,0xFF,0x7F,0xFF,0xFE,0xFF,0xFF,0xFD,0xFF,0xFF,0xFB,0xFF,0xFF,0xF7,0xFF,0xF0,0x0F,0xFF,0x80,0x1F,0xFE,0x00,0x3F,0xF8,0x00,0x7F,0xF0,0x00,0xFF,0xE0,0x01,0xFF,0xC0,0x03,0xFF,0x80,0x07,0xFF,0x00,0x0F,0xFE,0x00,0x1F,0xFC,0x00,0x3F,0xF8,0x00,0x7F,0xF0,0x00,0xFF,0xE0,0x01,0xFF,0xC0,0x03,0xFF,0x80,0x07,0xFF,0x00,0x0F,0xFE,0x00,0x1F,0xFC,0x00,0x3F,0xF8,0x00,0x7F,0xF0,0x00,0xFF,0xE0,0x01,0xFF,0xC0,0x03,0xFF,0x80,0x07,0xFF,0x00,0x0F,0xFE,0x00,0x1F,0xFC,0x00,0x00, // 'r'
	0x00,0x0F,0xFC,0x00,0x00,0x1F,0xFF,0xE0,0x00,0x1F,0xFF,0xFE,0x00,0x1F,0xFF,0xFF,0xC0,0x0F,0xFF,0xFF,0xFC,0x07,0xFF,0xFF,0xFF,0x01,0xFF,0xFF,0xFF,0xE0,0xFF,0xFF,0xFF,0xFC,0x3F,0xFC,0x1F,0xFF,0x0F,0xFE,0x03,0xFF,0xC7,0xFF,0x80,0x7F,0xF9,0xFF,0xC0,0x1F,0xFE,0x7F,0xF8,0x00,0x00,0x0F,0xFE,0x00,0x00,0x03,0xFF,0xE0,0x00,0x00,0xFF,0xFF,0x80,0x00,0x1F,0xFF,0xFE,0x00,0x07,0xFF,0xFF,0xF0,0x00,0xFF,0xFF,0xFF,0x00,0x1F,0xFF,0xFF,0xE0,0x03,0xFF,0xFF,0xFC,0x00,0x3F,0xFF,0xFF,0x80,0x01,0xFF,0xFF,0xF0,0x00,0x0F,0xFF,0xFC,0x00,0x00,0x3F,0xFF,0x00,0x00,0x03,0xFF,0xEF,0xFE,0x00,0x7F,0xFB,0xFF,0x80,0x0F,0xFE,0x7F,0xF0,0x03,0xFF,0x9F,0xFC,0x01,0xFF,0xE7,0xFF,0xC0,0xFF,0xF0,0xFF,0xFF,0xFF,0xFC,0x3F,0xFF,0xFF,0xFE,0x07,0xFF,0xFF,0xFF,0x80,0xFF,0xFF,0xFF,0xC0,0x1F,0xFF,0xFF,0xE0,0x01,0xFF,0xFF,0xE0,0x00,0x1F,0xFF,0xE0,0x00,0x00,0xFF,0xC0,0x00, // 's'
	0x07,0xFF,0x80,0x07,0xFF,0x80,0x07,0xFF,0x80,0x07,0xFF,0x80,0x07,0xFF,0x80,0x07,0xFF,0x80,0x07,0xFF,0x80,0x07,0xFF,0x80,0x07,0xFF,0x80,0xFF,0xFF,0xFE,0xFF,0xFF,0xFE,0xFF,0xFF,0xFE,0xFF,0xFF,0xFE,0xFF,0xFF,0xFE,0xFF,0xFF,0xFE,0xFF,0xFF,0xFE,0xFF,0xFF,0xFE,0x07,0xFF,0x80,0x07,0xFF,0x80,0x07,0xFF,0x80,0x07,0xFF,0x80,0x07,0xFF,0x80,0x07,0xFF,0x80,0x07,0xFF,0x80,0x07,0xFF,0x80,0x07,0xFF,0x80,0x07,0xFF,0x80,0x07,0xFF,0x80,0x07,0xFF,0x80,0x07,0xFF,0x80,0x07,0xFF,0x80,0x07,0xFF,0x80,0x07,0xFF,0x80,0x07,0xFF,0x80,0x07,0xFF,0x80,0x07,0xFF,0x80,0x07,0xFF,0x80,0x07,0xFF,0xC0,0x07,0xFF,0xFE,0x07,0xFF,0xFE,0x03,0xFF,0xFE,0x03,0xFF,0xFE,0x01,0xFF,0xFE,0x01,0xFF,0xFE,0x00,0xFF,0xFE,0x00,0x3F,0xFE,0x00,0x0F,0xF8, // 't'
	0xFF,0xE0,0x03,0xFF,0xBF,0xF8,0x00,0xFF,0xEF,0xFE,0x00,0x3F,0xFB,0xFF,0x80,0x0F,0xFE,0xFF,0xE0,0x03,0xFF,0xBF,0xF8,0x00,0xFF,0xEF,0xFE,0x00,0x3F,0xFB,0xFF,0x80,0x0F,0xFE,0xFF,0xE0,0x03,0xFF,0xBF,0xF8,0x00,0xFF,0xEF,0xFE,0x00,0x3F,0xFB,0xFF,0x80,0x0F,0xFE,0xFF,0xE0,0x03,0xFF,0xBF,0xF8,0x00,0xFF,0xEF,0xFE,0x00,0x3F,0xFB,0xFF,0x80,0x0F,0xFE,0xFF,0xE0,0x03,0xFF,0xBF,0xF8,0x00,0xFF,0xEF,0xFE,0x00,0x3F,0xFB,0xFF,0x80,0x0F,0xFE,0xFF,0xE0,0x03,0xFF,0xBF,0xF8,0x00,0xFF,0xEF,0xFE,0x00,0x3F,0xFB,0xFF,0x80,0x0F,0xFE,0xFF,0xE0,0x03,0xFF,0xBF,0xF8,0x00,0xFF,0xEF,0xFF,0x00,0x7F,0xFB,0xFF,0xC0,0x3F,0xFE,0xFF,0xF8,0x1F,0xFF,0xBF,0xFF,0xFF,0xFF,0xE7,0xFF,0xFF,0xFF,0xF9,0xFF,0xFF,0xFF,0xFE,0x3F,0xFF,0xFF,0xFF,0x8F,0xFF,0xFF,0xFF,0xE1,0xFF,0xFF,0xBF,0xF8,0x3F,0xFF,0xCF,0xFE,0x07,0xFF,0xC3,0xFF,0x80,0x3F,0xC0,0x00,0x00, // 'u'
	0xFF,0xF0,0x00,0xFF,0xF7,0xFF,0xC0,0x0F,0xFF,0x1F,0xFE,0x00,0x7F,0xF8,0xFF,0xF0,0x03,0xFF,0xC7,0xFF,0x80,0x1F,0xFC,0x1F,0xFE,0x01,0xFF,0xE0,0xFF,0xF0,0x0F,0xFF,0x07,0xFF,0x80,0x7F,0xF0,0x1F,0xFC,0x03,0xFF,0x80,0xFF,0xE0,0x3F,0xFC,0x07,0xFF,0x81,0xFF,0xC0,0x1F,0xFC,0x0F,0xFE,0x00,0xFF,0xE0,0x7F,0xF0,0x07,0xFF,0x07,0xFF,0x00,0x1F,0xFC,0x3F,0xF8,0x00,0xFF,0xE1,0xFF,0xC0,0x07,0xFF,0x0F,0xFE,0x00,0x1F,0xF8,0xFF,0xE0,0x00,0xFF,0xE7,0xFF,0x00,0x07,0xFF,0x3F,0xF8,0x00,0x1F,0xF9,0xFF,0x80,0x00,0xFF,0xDF,0xFC,0x00,0x07,0xFF,0xFF,0xE0,0x00,0x1F,0xFF,0xFE,0x00,0x00,0xFF,0xFF,0xF0,0x00,0x07,0xFF,0xFF,0x80,0x00,0x1F,0xFF,0xF8,0x00,0x00,0xFF,0xFF,0xC0,0x00,0x07,0xFF,0xFE,0x00,0x00,0x1F,0xFF,0xE0,0x00,0x00,0xFF,0xFF,0x00,0x00,0x07,0xFF,0xF8,0x00,0x00,0x1F,0xFF,0x80,0x00,0x00,0xFF,0xFC,0x00,0x00,0x07,0xFF,0xE0,0x00,0x00,0x1F,0xFE,0x00,0x00,0x00,0xFF,0xF0,0x00,0x00, // 'v'
	0xFF,0xE0,0x0F,0xF8,0x03,0xFF,0xBF,0xF8,0x03,0xFE,0x00,0xFF,0xEF,0xFE,0x00,0xFF,0x80,0x3F,0xF1,0xFF,0x80,0x7F,0xE0,0x1F,0xFC,0x7F,0xF0,0x1F,0xFC,0x07,0xFF,0x1F,0xFC,0x07,0xFF,0x01,0xFF,0xC7,0xFF,0x01,0xFF,0xC0,0x7F,0xF0,0xFF,0xC0,0xFF,0xF0,0x1F,0xF8,0x3F,0xF0,0x3F,0xFE,0x07,0xFE,0x0F,0xFC,0x0F,0xFF,0x83,0xFF,0x83,0xFF,0x83,0xFF,0xE0,0xFF,0xE0,0x7F,0xE1,0xFF,0xFC,0x3F,0xF0,0x1F,0xF8,0x7F,0xFF,0x0F,0xFC,0x07,0xFE,0x1F,0xFF,0xC3,0xFF,0x01,0xFF,0x87,0xFF,0xF1,0xFF,0xC0,0x7F,0xE3,0xFD,0xFE,0x7F,0xE0,0x0F,0xFC,0xFF,0x7F,0x9F,0xF8,0x03,0xFF,0x3F,0xDF,0xE7,0xFE,0x00,0xFF,0xCF,0xF7,0xF9,0xFF,0x80,0x3F,0xF7,0xF8,0xFF,0x7F,0xC0,0x07,0xFD,0xFE,0x3F,0xFF,0xF0,0x01,0xFF,0x7F,0x8F,0xFF,0xFC,0x00,0x7F,0xFF,0xC1,0xFF,0xFF,0x00,0x1F,0xFF,0xF0,0x7F,0xFF,0xC0,0x03,0xFF,0xFC,0x1F,0xFF,0xE0,0x00,0xFF,0xFF,0x07,0xFF,0xF8,0x00,0x3F,0xFF,0x80,0xFF,0xFE,0x00,0x0F,0xFF,0xE0,0x3F,0xFF,0x80,0x01,0xFF,0xF8,0x0F,0xFF,0xC0,0x00,0x7F,0xFC,0x03,0xFF,0xF0,0x00,0x1F,0xFF,0x00,0x7F,0xFC,0x00,0x07,0xFF,0xC0,0x1F,0xFF,0x00,0x01,0xFF,0xF0,0x07,0xFF,0x80,0x00,0x3F,0xF8,0x00,0xFF,0xE0,0x00,0x0F,0xFE,0x00,0x3F,0xF8,0x00,0x03,0xFF,0x80,0x0F,0xFE,0x00,0x00,0xFF,0xC0,0x03,0xFF,0x00,0x00, // 'w'
	0x7F,0xF8,0x01,0xFF,0xF1,0xFF,0xE0,0x0F,0xFF,0x0F,0xFF,0x00,0xFF,0xF0,0x3F,0xFC,0x07,0xFF,0x81,0xFF,0xE0,0x7F,0xF8,0x07,0xFF,0x83,0xFF,0xC0,0x3F,0xFC,0x3F,0xFC,0x00,0xFF,0xF1,0xFF,0xE0,0x07,0xFF,0x9F,0xFE,0x00,0x1F,0xFE,0xFF,0xE0,0x00,0x7F,0xFF,0xFF,0x00,0x03,0xFF,0xFF,0xF0,0x00,0x0F,0xFF,0xFF,0x80,0x00,0x7F,0xFF,0xF8,0x00,0x01,0xFF,0xFF,0xC0,0x00,0x0F,0xFF,0xFC,0x00,0x00,0x3F,0xFF,0xE0,0x00,0x01,0xFF,0xFE,0x00,0x00,0x0F,0xFF,0xF0,0x00,0x00,0x7F,0xFF,0xC0,0x00,0x07,0xFF,0xFE,0x00,0x00,0x3F,0xFF,0xF8,0x00,0x03,0xFF,0xFF,0xC0,0x00,0x1F,0xFF,0xFF,0x00,0x01,0xFF,0xFF,0xF8,0x00,0x0F,0xFF,0xFF,0xE0,0x00,0xFF,0xFF,0xFF,0x00,0x0F,0xFF,0x3F,0xFC,0x00,0x7F,0xF9,0xFF,0xE0,0x07,0xFF,0x87,0xFF,0x80,0x3F,0xFC,0x1F,0xFE,0x03,0xFF,0xC0,0xFF,0xF0,0x1F,0xFE,0x03,0xFF,0xC1,0xFF,0xE0,0x1F,0xFE,0x0F,0xFF,0x00,0x7F,0xF8,0xFF,0xF0,0x03,0xFF,0xCF,0xFF,0x80,0x0F,0xFF,0x00, // 'x'
	0xFF,0xF0,0x00,0xFF,0xF7,0xFF,0x80,0x07,0xFF,0x9F,0xFE,0x00,0x7F,0xFC,0xFF,0xF0,0x03,0xFF,0xC7,0xFF,0x80,0x1F,0xFE,0x1F,0xFE,0x01,0xFF,0xE0,0xFF,0xF0,0x0F,0xFF,0x07,0xFF,0x80,0x7F,0xF8,0x1F,0xFC,0x03,0xFF,0x80,0xFF,0xF0,0x3F,0xFC,0x07,0xFF,0x81,0xFF,0xE0,0x1F,0xFC,0x0F,0xFE,0x00,0xFF,0xF0,0xFF,0xF0,0x07,0xFF,0x87,0xFF,0x80,0x1F,0xFC,0x3F,0xF8,0x00,0xFF,0xE3,0xFF,0xC0,0x07,0xFF,0x9F,0xFE,0x00,0x1F,0xFC,0xFF,0xE0,0x00,0xFF,0xE7,0xFF,0x00,0x07,0xFF,0x7F,0xF8,0x00,0x1F,0xFF,0xFF,0x80,0x00,0xFF,0xFF,0xFC,0x00,0x07,0xFF,0xFF,0xE0,0x00,0x1F,0xFF,0xFE,0x00,0x00,0xFF,0xFF,0xF0,0x00,0x03,0xFF,0xFF,0x00,0x00,0x1F,0xFF,0xF8,0x00,0x00,0xFF,0xFF,0xC0,0x00,0x03,0xFF,0xFC,0x00,0x00,0x1F,0xFF,0xE0,0x00,0x00,0xFF,0xFF,0x00,0x00,0x03,0xFF,0xF0,0x00,0x00,0x1F,0xFF,0x80,0x00,0x00,0xFF,0xFC,0x00,0x00,0x03,0xFF,0xC0,0x00,0x00,0x1F,0xFE,0x00,0x00,0x00,0xFF,0xF0,0x00,0x00,0x03,0xFF,0x00,0x00,0x00,0x3F,0xF8,0x00,0x00,0x01,0xFF,0xC0,0x00,0x00,0x1F,0xFC,0x00,0x00,0x00,0xFF,0xE0,0x00,0x00,0x1F,0xFF,0x00,0x00,0x0F,0xFF,0xF0,0x00,0x00,0x7F,0xFF,0x80,0x00,0x03,0xFF,0xF8,0x00,0x00,0x1F,0xFF,0xC0,0x00,0x00,0xFF,0xFC,0x00,0x00,0x07,0xFF,0xC0,0x00,0x00,0x3F,0xFC,0x00,0x00,0x01,0xFF,0xC0,0x00,0x00,0x0F,0xF0,0x00,0x00,0x00, // 'y'
	0x7F,0xFF,0xFF,0xFE,0x3F,0xFF,0xFF,0xFF,0x1F,0xFF,0xFF,0xFF,0x8F,0xFF,0xFF,0xFF,0xC7,0xFF,0xFF,0xFF,0xE3,0xFF,0xFF,0xFF,0xF1,0xFF,0xFF,0xFF,0xF8,0xFF,0xFF,0xFF,0xF8,0x7F,0xFF,0xFF,0xFC,0x00,0x00,0x7F,0xFC,0x00,0x00,0x7F,0xFC,0x00,0x00,0x3F,0xFC,0x00,0x00,0x3F,0xFE,0x00,0x00,0x3F,0xFE,0x00,0x00,0x3F,0xFE,0x00,0x00,0x1F,0xFE,0x00,0x00,0x1F,0xFF,0x00,0x00,0x1F,0xFF,0x00,0x00,0x1F,0xFF,0x00,0x00,0x0F,0xFF,0x00,0x00,0x0F,0xFF,0x80,0x00,0x0F,0xFF,0x80,0x00,0x0F,0xFF,0x80,0x00,0x07,0xFF,0x80,0x00,0x07,0xFF,0x80,0x00,0x07,0xFF,0xC0,0x00,0x07,0xFF,0xC0,0x00,0x03,0xFF,0xC0,0x00,0x03,0xFF,0xFF,0xFF,0xF3,0xFF,0xFF,0xFF,0xFB,0xFF,0xFF,0xFF,0xFD,0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,0xFF,0xFF,0x7F,0xFF,0xFF,0xFF,0xBF,0xFF,0xFF,0xFF,0xDF,0xFF,0xFF,0xFF,0xEF,0xFF,0xFF,0xFF,0xF0, // 'z'
	0x00,0x00,0xC0,0x00,0x7C,0x00,0x0F,0xC0,0x03,0xFE,0x00,0x7F,0xE0,0x0F,0xFE,0x00,0xFF,0xE0,0x1F,0xF8,0x03,0xFF,0x80,0x3F,0xF0,0x03,0xFF,0x00,0x7F,0xE0,0x07,0xFE,0x00,0x7F,0xE0,0x07,0xFE,0x00,0x7F,0xE0,0x07,0xFE,0x00,0x7F,0xE0,0x07,0xFE,0x00,0x7F,0xE0,0x07,0xFE,0x00,0x7F,0xE0,0x07,0xFE,0x00,0x7F,0xE0,0x07,0xFE,0x00,0x7F,0xE0,0x07,0xFE,0x00,0xFF,0xC0,0x1F,0xFC,0x03,0xFF,0xC0,0xFF,0xF8,0x0F,0xFF,0x00,0xFF,0xE0,0x0F,0xFC,0x00,0xFF,0xC0,0x0F,0xFE,0x00,0xFF,0xF0,0x0F,0xFF,0x80,0x3F,0xFC,0x01,0xFF,0xC0,0x0F,0xFC,0x00,0x7F,0xE0,0x07,0xFE,0x00,0x7F,0xE0,0x07,0xFE,0x00,0x7F,0xE0,0x07,0xFE,0x00,0x7F,0xE0,0x07,0xFE,0x00,0x7F,0xE0,0x07,0xFE,0x00,0x7F,0xE0,0x07,0xFE,0x00,0x7F,0xE0,0x07,0xFE,0x00,0x7F,0xE0,0x03,0xFF,0x00,0x3F,0xF0,0x03,0xFF,0x00,0x1F,0xF8,0x01,0xFF,0xC0,0x0F,0xFE,0x00,0x7F,0xE0,0x03,0xFE,0x00,0x1F,0xC0,0x00,0x7C,0x00,0x00,0xC0, // '{'
	0xFD,0xFB,0xF7,0xEF,0xDF,0xBF,0x7E,0xFD,0xFB,0xF7,0xEF,0xDF,0xBF,0x7E,0xFD,0xFB,0xF7,0xEF,0xDF,0xBF,0x7E,0xFD,0xFB,0xF7,0xEF,0xDF,0xBF,0x7E,0xFD,0xFB,0xF7,0xEF,0xDF,0xBF,0x7E,0xFD,0xFB,0xF7,0xEF,0xDF,0xBF,0x7E,0xFD,0xFB,0xF7,0xEF,0xDF,0xBF,0x7E,0xFD,0xFB,0xF0, // '|'
	0x20,0x00,0x03,0xE0,0x00,0x1F,0x80,0x01,0xFF,0x00,0x0F,0xFC,0x00,0x7F,0xF0,0x03,0xFF,0x80,0x07,0xFE,0x00,0x3F,0xF8,0x00,0xFF,0xC0,0x07,0xFE,0x00,0x1F,0xF8,0x00,0xFF,0xC0,0x07,0xFE,0x00,0x3F,0xF0,0x01,0xFF,0x80,0x0F,0xFC,0x00,0x7F,0xE0,0x03,0xFF,0x00,0x1F,0xF8,0x00,0xFF,0xC0,0x07,0xFE,0x00,0x3F,0xF0,0x01,0xFF,0x80,0x0F,0xFC,0x00,0x7F,0xE0,0x03,0xFF,0x00,0x0F,0xFC,0x00,0x7F,0xF0,0x03,0xFF,0xC0,0x0F,0xFF,0xC0,0x3F,0xFE,0x00,0xFF,0xF0,0x03,0xFF,0x80,0x1F,0xFC,0x01,0xFF,0xE0,0x1F,0xFF,0x01,0xFF,0xF8,0x1F,0xFE,0x00,0xFF,0xE0,0x07,0xFE,0x00,0x7F,0xE0,0x03,0xFF,0x00,0x1F,0xF8,0x00,0xFF,0xC0,0x07,0xFE,0x00,0x3F,0xF0,0x01,0xFF,0x80,0x0F,0xFC,0x00,0x7F,0xE0,0x03,0xFF,0x00,0x1F,0xF8,0x00,0xFF,0xC0,0x07,0xFE,0x00,0x3F,0xF0,0x01,0xFF,0x80,0x1F,0xF8,0x00,0xFF,0xC0,0x0F,0xFE,0x00,0x7F,0xE0,0x07,0xFF,0x00,0x7F,0xF0,0x03,0xFF,0x00,0x1F,0xF0,0x00,0x7F,0x00,0x03,0xE0,0x00,0x08,0x00,0x00 // '}'
};
const GFXglyph Roboto_Black_70Glyphs[] PROGMEM = {
// bitmapOffset, width, height, xAdvance, xOffset, yOffset
	  {     0,   1,   1,  18,    0,    0 }, // ' '
	  {     1,  14,  51,  20,    3,  -50 }, // '!'
	  {    91,  21,  20,  23,    1,  -53 }, // '"'
	  {   144,  39,  50,  42,    1,  -50 }, // '#'
	  {   388,  36,  65,  42,    3,  -57 }, // '$'
	  {   681,  47,  51,  53,    3,  -50 }, // '%'
	  {   981,  45,  51,  48,    2,  -50 }, // '&'
	  {  1268,   9,  20,  12,    2,  -53 }, // '''
	  {  1291,  21,  70,  26,    4,  -55 }, // '('
	  {  1475,  21,  70,  26,    2,  -55 }, // ')'
	  {  1659,  32,  31,  34,    1,  -50 }, // '*'
	  {  1783,  35,  35,  38,    2,  -41 }, // '+'
	  {  1937,  13,  22,  20,    2,   -9 }, // ','
	  {  1973,  22,   9,  32,    5,  -26 }, // '-'
	  {  1998,  14,  11,  22,    4,  -11 }, // '.'
	  {  2018,  26,  54,  26,   -1,  -50 }, // '/'
	  {  2194,  36,  51,  42,    3,  -50 }, // '0'
	  {  2424,  24,  50,  42,    6,  -50 }, // '1'
	  {  2574,  37,  50,  42,    2,  -50 }, // '2'
	  {  2806,  37,  51,  42,    2,  -50 }, // '3'
	  {  3042,  38,  50,  42,    2,  -50 }, // '4'
	  {  3280,  36,  51,  42,    3,  -50 }, // '5'
	  {  3510,  37,  51,  42,    3,  -50 }, // '6'
	  {  3746,  37,  50,  42,    2,  -50 }, // '7'
	  {  3978,  36,  51,  42,    3,  -50 }, // '8'
	  {  4208,  35,  51,  42,    3,  -50 }, // '9'
	  {  4432,  14,  38,  22,    4,  -38 }, // ':'
	  {  4499,  15,  51,  21,    3,  -38 }, // ';'
	  {  4595,  31,  34,  37,    2,  -38 }, // '<'
	  {  4727,  33,  24,  42,    4,  -34 }, // '='
	  {  4826,  31,  34,  37,    4,  -38 }, // '>'
	  {  4958,  33,  50,  37,    1,  -50 }, // '?'
	  {  5165,  59,  63,  64,    2,  -48 }, // '@'
	  {  5630,  49,  50,  49,    0,  -50 }, // 'A'
	  {  5937,  39,  50,  46,    4,  -50 }, // 'B'
	  {  6181,  43,  51,  47,    2,  -50 }, // 'C'
	  {  6456,  40,  50,  46,    4,  -50 }, // 'D'
	  {  6706,  35,  50,  40,    4,  -50 }, // 'E'
	  {  6925,  34,  50,  39,    4,  -50 }, // 'F'
	  {  7138,  43,  51,  49,    3,  -50 }, // 'G'
	  {  7413,  42,  50,  50,    4,  -50 }, // 'H'
	  {  7676,  13,  50,  22,    5,  -50 }, // 'I'
	  {  7758,  35,  51,  40,    1,  -50 }, // 'J'
	  {  7982,  43,  50,  46,    4,  -50 }, // 'K'
	  {  8251,  34,  50,  39,    4,  -50 }, // 'L'
	  {  8464,  54,  50,  62,    4,  -50 }, // 'M'
	  {  8802,  42,  50,  50,    4,  -50 }, // 'N'
	  {  9065,  45,  51,  49,    2,  -50 }, // 'O'
	  {  9352,  40,  50,  47,    4,  -50 }, // 'P'
	  {  9602,  45,  59,  49,    2,  -50 }, // 'Q'
	  {  9934,  41,  50,  47,    4,  -50 }, // 'R'
	  { 10191,  41,  51,  45,    2,  -50 }, // 'S'
	  { 10453,  43,  50,  45,    1,  -50 }, // 'T'
	  { 10722,  40,  51,  48,    4,  -50 }, // 'U'
	  { 10977,  47,  50,  47,    0,  -50 }, // 'V'
	  { 11271,  60,  50,  62,    1,  -50 }, // 'W'
	  { 11646,  46,  50,  46,    0,  -50 }, // 'X'
	  { 11934,  45,  50,  45,    0,  -50 }, // 'Y'
	  { 12216,  40,  50,  44,    2,  -50 }, // 'Z'
	  { 12466,  16,  70,  21,    4,  -58 }, // '['
	  { 12606,  34,  54,  31,   -1,  -50 }, // '\'
	  { 12836,  17,  70,  21,    0,  -58 }, // ']'
	  { 12985,  30,  25,  32,    1,  -50 }, // '^'
	  { 13079,  32,   9,  32,    0,    0 }, // '_'
	  { 13115,  20,  11,  25,    2,  -53 }, // '`'
	  { 13143,  35,  39,  38,    2,  -38 }, // 'a'
	  { 13314,  35,  54,  40,    3,  -53 }, // 'b'
	  { 13551,  34,  39,  37,    2,  -38 }, // 'c'
	  { 13717,  35,  54,  40,    2,  -53 }, // 'd'
	  { 13954,  36,  39,  39,    2,  -38 }, // 'e'
	  { 14130,  26,  53,  26,    0,  -53 }, // 'f'
	  { 14303,  36,  53,  41,    2,  -38 }, // 'g'
	  { 14542,  35,  53,  41,    3,  -53 }, // 'h'
	  { 14774,  14,  52,  20,    3,  -52 }, // 'i'
	  { 14865,  21,  67,  20,   -4,  -52 }, // 'j'
	  { 15041,  37,  53,  39,    3,  -53 }, // 'k'
	  { 15287,  12,  53,  20,    4,  -53 }, // 'l'
	  { 15367,  55,  38,  61,    3,  -38 }, // 'm'
	  { 15629,  34,  38,  40,    3,  -38 }, // 'n'
	  { 15791,  36,  39,  40,    2,  -38 }, // 'o'
	  { 15967,  35,  52,  40,    3,  -38 }, // 'p'
	  { 16195,  35,  52,  40,    2,  -38 }, // 'q'
	  { 16423,  23,  38,  28,    4,  -38 }, // 'r'
	  { 16533,  34,  39,  37,    1,  -38 }, // 's'
	  { 16699,  24,  47,  25,    0,  -46 }, // 't'
	  { 16840,  34,  38,  40,    3,  -37 }, // 'u'
	  { 17002,  37,  37,  37,    0,  -37 }, // 'v'
	  { 17174,  50,  37,  52,    1,  -37 }, // 'w'
	  { 17406,  37,  37,  37,    0,  -37 }, // 'x'
	  { 17578,  37,  52,  37,    0,  -37 }, // 'y'
	  { 17819,  33,  37,  37,    2,  -37 }, // 'z'
	  { 17972,  20,  67,  24,    2,  -55 }, // '{'
	  { 18140,   7,  59,  19,    6,  -50 }, // '|'
	  { 18192,  21,  67,  24,    2,  -55 } // '}'
};
const GFXfont Roboto_Black_70 PROGMEM = {
(uint8_t  *)Roboto_Black_70Bitmaps,(GFXglyph *)Roboto_Black_70Glyphs,0x20, 0x7E, 83};

